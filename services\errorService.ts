// Helper function to extract a string message from an unknown error
export const getErrorMessage = (error: unknown): string => {
    const defaultMessage = 'Une erreur est survenue. Veuillez réessayer.';

    // The most reliable check: if it's a real Error object, use its message.
    if (error instanceof Error) {
        // Avoid returning useless "[object Object]" string
        if (error.message && error.message !== '[object Object]') {
            return error.message;
        }
    }
    
    // If it's a plain object that looks like a Supabase error.
    // This is often a class instance extending Error, so the above check is preferred.
    // This is a fallback for other error shapes.
    if (error && typeof error === 'object' && 'message' in error) {
        const msg = (error as {message: unknown}).message;
        if (typeof msg === 'string' && msg) {
            return msg;
        }
    }

    // If it's a simple string, return it.
    if (typeof error === 'string' && error.length > 0) {
        return error;
    }
    
    // For anything else (DOM elements, React internals, circular objects), give up immediately
    // and return a default message. This is the key to prevent downstream serialization crashes.
    return defaultMessage;
};