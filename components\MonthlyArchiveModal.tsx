
import React, { useState } from 'react';
import { Icon } from './shared/Icon';
import { Spinner } from './shared/Spinner';

interface MonthlyArchiveModalProps {
  onClose: () => void;
  onArchive: (month: number, year: number, periodLabel: string) => Promise<void>;
}

const months = [
    "Jan<PERSON>", "Février", "Mars", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>", 
    "<PERSON>ille<PERSON>", "Aoû<PERSON>", "Sept<PERSON>bre", "Octobre", "Novembre", "Décembre"
];

export const MonthlyArchiveModal: React.FC<MonthlyArchiveModalProps> = ({ onClose, onArchive }) => {
  const [isArchiving, setIsArchiving] = useState(false);
  
  const today = new Date();
  const prevMonthDate = new Date(today.getFullYear(), today.getMonth() - 1, 1);
  const [year, setYear] = useState(prevMonthDate.getFullYear());
  const [month, setMonth] = useState(prevMonthDate.getMonth() + 1);

  const handleArchive = async () => {
    setIsArchiving(true);
    const periodLabel = `${months[month - 1]} ${year}`;
    await onArchive(month, year, periodLabel);
    setIsArchiving(false);
    onClose();
  };

  const years = Array.from({ length: 5 }, (_, i) => today.getFullYear() - i);

  return (
    <div className="fixed inset-0 bg-black bg-opacity-60 flex justify-center items-center z-50 p-4">
      <div className="bg-white rounded-xl shadow-2xl w-full max-w-lg p-8 m-4 modal-content-animate">
        <div className="flex justify-between items-center mb-5">
            <h2 className="text-2xl font-bold text-gray-800">
                Archiver le Rapport Mensuel
            </h2>
             <button onClick={onClose} className="text-gray-400 hover:text-gray-600">
                <Icon.Close className="h-6 w-6" />
            </button>
        </div>
        
        <p className="text-gray-600 mt-2">
          Veuillez sélectionner le mois et l'année pour lesquels vous souhaitez générer et archiver le rapport d'absences. Cette action créera un instantané permanent des données pour cette période.
        </p>

        <div className="mt-6 flex gap-4 items-end">
          <div className="flex-1">
            <label htmlFor="month-select" className="block text-sm font-medium text-gray-700 mb-1">Mois</label>
            <select
              id="month-select"
              value={month}
              onChange={(e) => setMonth(Number(e.target.value))}
              className="w-full p-2 border border-gray-300 rounded-lg focus:ring-[--lica-blue] focus:border-[--lica-blue]"
            >
              {months.map((m, i) => (
                <option key={i} value={i + 1}>{m}</option>
              ))}
            </select>
          </div>
          <div className="flex-1">
            <label htmlFor="year-select" className="block text-sm font-medium text-gray-700 mb-1">Année</label>
            <select
              id="year-select"
              value={year}
              onChange={(e) => setYear(Number(e.target.value))}
              className="w-full p-2 border border-gray-300 rounded-lg focus:ring-[--lica-blue] focus:border-[--lica-blue]"
            >
              {years.map(y => (
                <option key={y} value={y}>{y}</option>
              ))}
            </select>
          </div>
        </div>

        <div className="mt-8 flex flex-col sm:flex-row justify-end gap-4">
           <button 
            type="button" 
            onClick={onClose} 
            className="py-2 px-4 bg-gray-200 text-gray-800 font-semibold rounded-lg hover:bg-gray-300"
          >
            Annuler
          </button>
          <button 
            onClick={handleArchive}
            disabled={isArchiving}
            className="w-full sm:w-auto flex items-center justify-center gap-2 px-4 py-2 text-sm font-semibold text-white bg-[--org-color-main] rounded-lg hover:bg-[--org-color-dark] transition-colors disabled:bg-opacity-60"
          >
            {isArchiving ? <> <Spinner size="sm" color="white"/> <span>Archivage...</span> </> : <><Icon.ArchiveBox className="h-5 w-5 mr-1" /> Archiver le rapport</>}
          </button>
        </div>
      </div>
    </div>
  );
};