import React from 'react';
import { Icon } from './shared/Icon';
import { Spinner } from './shared/Spinner';
import type { Organization } from '../types';
import { generateColorFromString } from '../services/colorService';

interface OrganizationSelectorProps {
  organizations: Organization[];
  onSelect: (org: Organization) => void;
  onAdd: () => void;
  onEdit: (org: Organization) => void;
  onLogout: () => void;
  isLoading: boolean;
  onDelete: (org: Organization) => void;
}

export const OrganizationSelector: React.FC<OrganizationSelectorProps> = ({
  organizations,
  onSelect,
  onAdd,
  onEdit,
  onLogout,
  isLoading,
  onDelete,
}) => {

  return (
    <div className="min-h-screen bg-[--lica-blue-light]">
      <header className="bg-white shadow-sm">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div className="flex justify-between items-center py-4">
                <Icon.LicaFullLogo className="h-12 w-auto text-[--lica-blue]" />
                <button
                    onClick={onLogout}
                    className="flex items-center p-2 rounded-lg transition-all duration-200 text-gray-600 hover:bg-red-50 hover:text-red-600 font-semibold"
                >
                    <Icon.Logout className="h-5 w-5" />
                    <span className="ml-2 text-sm">Se déconnecter</span>
                </button>
            </div>
        </div>
      </header>

      <main className="max-w-7xl mx-auto py-8 px-4 sm:px-6 lg:px-8">
        <div className="flex justify-between items-center mb-8 pb-4 border-b border-gray-200">
            <h1 className="text-3xl font-bold text-[--lica-blue-dark] uppercase tracking-wide">Mes Organisations</h1>
            <button
                onClick={onAdd}
                className="flex items-center bg-[--lica-blue] text-white font-semibold py-2 px-5 rounded-lg shadow-md hover:bg-[--lica-blue-dark] hover:scale-105 transform transition-all duration-200 uppercase tracking-wider"
            >
                <Icon.Plus className="h-5 w-5 mr-2"/>
                Nouvelle Organisation
            </button>
        </div>

        {isLoading ? (
          <div className="flex justify-center items-center py-20">
            <Spinner size="lg" />
          </div>
        ) : organizations.length === 0 ? (
          <div className="text-center py-20">
            <Icon.ArchiveBox className="h-16 w-16 text-gray-400 mx-auto mb-4"/>
            <h3 className="text-xl font-semibold text-gray-700">Aucune organisation cliente trouvée.</h3>
            <p className="text-gray-500 mt-2">Commencez par ajouter votre première organisation pour démarrer un audit.</p>
          </div>
        ) : (
          <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-6">
            {organizations.map((org) => {
                const colors = generateColorFromString(org.id);
                return (
              <div
                key={org.id}
                className="bg-white border border-gray-200/80 rounded-xl shadow-sm card-hover text-left flex flex-col items-start justify-between relative group"
              >
                <div className="p-6 w-full h-full flex flex-col items-start justify-between">
                    <div>
                        <div className="p-3 rounded-lg inline-block mb-4" style={{ backgroundColor: colors.light }}>
                            <Icon.Scale className="h-8 w-8" style={{ color: colors.main }} />
                        </div>
                        <h2 className="font-bold text-xl" style={{ color: colors.main }}>{org.name}</h2>
                    </div>
                    <p className="text-sm text-gray-500 mt-2">Créée le: {new Date(org.created_at).toLocaleDateString('fr-FR')}</p>
                </div>
                
                <button 
                    onClick={() => onSelect(org)} 
                    className="absolute inset-0 z-10"
                    aria-label={`Ouvrir ${org.name}`}
                />

                <div className="absolute top-2 right-2 z-20 flex items-center opacity-0 group-hover:opacity-100 transition-opacity">
                     <button
                        onClick={(e) => { e.stopPropagation(); onEdit(org); }}
                        className="p-2 text-gray-500 hover:bg-yellow-100 hover:text-yellow-700 rounded-full"
                        title={`Modifier ${org.name}`}
                    >
                        <Icon.Pencil className="w-5 h-5" />
                    </button>
                    <button
                        onClick={(e) => {
                            e.stopPropagation();
                            onDelete(org);
                        }}
                        className="p-2 text-gray-500 hover:bg-red-100 hover:text-red-600 rounded-full"
                        title={`Supprimer ${org.name}`}
                    >
                        <Icon.Trash className="w-5 h-5" />
                    </button>
                </div>
              </div>
            )})}
          </div>
        )}
      </main>
    </div>
  );
};