import React from 'react';
import { Employee } from '../types';
import { Card } from './shared/Card';
import { Icon } from './shared/Icon';

interface EmployeesViewProps {
  employees: Employee[];
  onAddEmployee: () => void;
  onSelectProfile: (employee: Employee) => void;
  onOpenBatchUpload: (employee: Employee) => void;
}

const EmployeeCard: React.FC<{ 
    employee: Employee; 
    onSelectProfile: () => void;
    onOpenBatchUpload: () => void;
}> = ({ employee, onSelectProfile, onOpenBatchUpload }) => {
    
    const getInitials = (name: string) => {
        return name.split(' ').map(n => n[0]).join('').toUpperCase();
    }

    const today = new Date();
    today.setHours(0, 0, 0, 0);
    const contractEndDate = employee.contractEndDate ? new Date(employee.contractEndDate) : null;
    const isActive = !contractEndDate || contractEndDate >= today;

    return (
        <Card className="p-0 flex flex-col justify-between">
            <div className="p-5 text-left w-full h-full flex-grow relative">
                <div className="flex items-center space-x-4">
                    <div className="flex-shrink-0 h-12 w-12 rounded-full bg-[--org-color-light] flex items-center justify-center">
                        <span className="text-lg font-bold text-[--org-color-main]">{getInitials(employee.name)}</span>
                    </div>
                    <div>
                        <h3 className="font-bold text-gray-800 text-lg">{employee.name}</h3>
                        <p className="text-sm text-gray-500">{employee.position}</p>
                    </div>
                </div>
                <div className="mt-4 space-y-2">
                    <div className="flex items-center text-sm text-gray-600">
                        {isActive ? <Icon.CheckCircle className="h-5 w-5 text-green-500 mr-2" /> : <Icon.Warning className="h-5 w-5 text-red-500 mr-2" />}
                        Statut :
                        <span className={`font-semibold ml-1 ${isActive ? 'text-green-600' : 'text-red-600'}`}>
                            {isActive ? 'Actif' : 'N\'est plus en poste'}
                        </span>
                    </div>
                    {employee.contractStartDate && (
                        <div className="flex items-center text-sm text-gray-600">
                            <Icon.Calendar className="h-5 w-5 text-gray-400 mr-2" />
                            Début du contrat :
                            <span className="font-semibold text-gray-800 ml-1">
                                {new Date(employee.contractStartDate).toLocaleDateString('fr-FR')}
                            </span>
                        </div>
                    )}
                </div>
                 <button onClick={onSelectProfile} className="absolute inset-0 z-0" aria-label={`Voir le profil de ${employee.name}`}></button>
            </div>
            <div className="border-t border-gray-200 p-2 bg-gray-50/70 flex justify-end">
                 <button onClick={onOpenBatchUpload} className="relative z-10 flex items-center text-sm font-semibold text-[--org-color-main] hover:bg-[--org-color-light] py-1 px-3 rounded-md transition-colors">
                    <Icon.Upload className="w-4 h-4 mr-2" />
                    Documents (OCR)
                </button>
            </div>
        </Card>
    );
}

export const EmployeesView: React.FC<EmployeesViewProps> = ({ employees, onAddEmployee, onSelectProfile, onOpenBatchUpload }) => {

  return (
    <div>
      <div className="flex justify-between items-center mb-8 pb-4 border-b border-gray-200">
        <h1 className="text-3xl font-bold text-[--org-color-main] uppercase tracking-wide">Gestion des Employés</h1>
        <button
        onClick={onAddEmployee}
        className="flex items-center bg-[--org-color-main] text-white font-semibold py-2 px-5 rounded-lg shadow-md hover:bg-[--org-color-dark] hover:scale-105 transform transition-all duration-200 uppercase tracking-wider"
        >
        <Icon.Plus className="h-5 w-5 mr-2"/>
        Ajouter un employé
        </button>
      </div>
      
      <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6">
        {employees.map(employee => (
          <EmployeeCard 
            key={employee.id} 
            employee={employee}
            onSelectProfile={() => onSelectProfile(employee)}
            onOpenBatchUpload={() => onOpenBatchUpload(employee)}
          />
        ))}
      </div>
    </div>
  );
};