
import React from 'react';
import { HRDocument } from '../types';
import { Icon } from './shared/Icon';

export const ComplianceAnalysisModal: React.FC<{
  document: HRDocument;
  onClose: () => void;
}> = ({ document, onClose }) => {
  if (!document.contentComplianceAnalysis) return null;

  const analysis = document.contentComplianceAnalysis;

  return (
    <div className="fixed inset-0 bg-black bg-opacity-60 flex justify-center items-center z-50 p-4">
      <div className="bg-white rounded-xl shadow-2xl w-full max-w-4xl max-h-[90vh] flex flex-col">
        {/* Header */}
        <div className="flex justify-between items-center p-5 border-b border-gray-200">
          <div>
            <h2 className="text-xl font-bold text-gray-800">Rapport de Conformité du Document</h2>
            <p className="text-sm text-gray-500 truncate" title={document.name}>{document.name} ({document.category})</p>
          </div>
          <button onClick={onClose} className="text-gray-400 hover:text-gray-600"><Icon.Close className="h-6 w-6" /></button>
        </div>

        {/* Body */}
        <div className="flex-grow overflow-y-auto p-6 grid grid-cols-1 lg:grid-cols-2 gap-6">
          <div className="bg-gray-100 rounded-lg flex items-center justify-center p-4 border border-gray-200 min-h-[300px]">
            <img src={document.url} alt={document.name} className="max-w-full max-h-[60vh] object-contain rounded-md" />
          </div>
          <div>
            <h3 className="text-lg font-semibold text-[--lica-blue] mb-4 flex items-center">
              <Icon.Sparkles className="h-5 w-5 mr-2" />
              Analyse par IA
            </h3>
            <div className="space-y-6">
                <div className={`p-4 rounded-lg flex items-center ${analysis.isCompliant ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800'}`}>
                    {analysis.isCompliant ? <Icon.CheckCircle className="h-6 w-6 mr-3" /> : <Icon.Warning className="h-6 w-6 mr-3" />}
                    <span className="font-semibold">{analysis.isCompliant ? 'Document jugé conforme' : 'Anomalies de conformité détectées'}</span>
                </div>
                <div>
                    <h4 className="font-semibold text-gray-700 mb-2">Résumé de l'IA :</h4>
                    <p className="text-sm text-gray-600 bg-gray-50 p-3 rounded-lg border">{analysis.summary}</p>
                </div>
                
                {analysis.issues.length > 0 && (
                     <div>
                        <h4 className="font-semibold text-red-700 mb-2">Points non-conformes ou manquants :</h4>
                        <ul className="space-y-1">
                            {analysis.issues.map((issue, index) => (
                                <li key={index} className="flex items-start text-sm text-red-800 bg-red-50 p-2 rounded-md">
                                    <Icon.Warning className="h-4 w-4 mr-2 mt-0.5 flex-shrink-0 text-red-500" />
                                    <span>{issue}</span>
                                </li>
                            ))}
                        </ul>
                    </div>
                )}
              
                {analysis.findings.length > 0 && (
                    <div>
                        <h4 className="font-semibold text-green-700 mb-2">Points de conformité validés :</h4>
                        <ul className="space-y-1">
                            {analysis.findings.map((finding, index) => (
                                <li key={index} className="flex items-start text-sm text-green-800 bg-green-50 p-2 rounded-md">
                                    <Icon.CheckCircle className="h-4 w-4 mr-2 mt-0.5 flex-shrink-0 text-green-600" />
                                    <span>{finding}</span>
                                </li>
                            ))}
                        </ul>
                    </div>
                )}
            </div>
          </div>
        </div>
        {/* Footer */}
        <div className="p-4 bg-gray-50 border-t border-gray-200 flex justify-end">
            <button type="button" onClick={onClose} className="py-2 px-4 bg-gray-200 text-gray-800 font-semibold rounded-lg hover:bg-gray-300">Fermer</button>
        </div>
      </div>
    </div>
  );
};
