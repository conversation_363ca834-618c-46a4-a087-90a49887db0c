import React, { useState, useEffect } from 'react';
import { getRateLimiterStatus } from '../../services/geminiService';
import { Icon } from './Icon';

interface RateLimitStatusProps {
  show?: boolean;
}

export const RateLimitStatus: React.FC<RateLimitStatusProps> = ({ show = false }) => {
  const [status, setStatus] = useState<any>(null);
  const [isVisible, setIsVisible] = useState(false);

  useEffect(() => {
    if (show) {
      const updateStatus = () => {
        const currentStatus = getRateLimiterStatus();
        setStatus(currentStatus);
        
        // Show status if there are queued requests or we're near the limit
        setIsVisible(
          currentStatus.queueLength > 0 || 
          currentStatus.requestsInLastMinute >= currentStatus.maxRequestsPerMinute * 0.8
        );
      };

      updateStatus();
      const interval = setInterval(updateStatus, 1000);
      
      return () => clearInterval(interval);
    } else {
      setIsVisible(false);
    }
  }, [show]);

  if (!isVisible || !status) {
    return null;
  }

  const isNearLimit = status.requestsInLastMinute >= status.maxRequestsPerMinute * 0.8;
  const isAtLimit = !status.canMakeRequest;

  return (
    <div className={`fixed bottom-4 right-4 z-50 p-3 rounded-lg shadow-lg text-white text-sm max-w-xs ${
      isAtLimit ? 'bg-red-600' : isNearLimit ? 'bg-yellow-600' : 'bg-blue-600'
    }`}>
      <div className="flex items-center space-x-2">
        <Icon.Clock className="h-4 w-4 flex-shrink-0" />
        <div>
          <div className="font-medium">
            API Gemini {isAtLimit ? 'Limite atteinte' : isNearLimit ? 'Proche limite' : 'En cours'}
          </div>
          <div className="text-xs opacity-90">
            {status.requestsInLastMinute}/{status.maxRequestsPerMinute} requêtes/min
            {status.queueLength > 0 && ` • ${status.queueLength} en attente`}
          </div>
        </div>
      </div>
    </div>
  );
};
