import React from 'react';

interface ChartSegment {
  value: number;
  color: string;
  label: string;
}

interface DoughnutChartProps {
  data: ChartSegment[];
  size?: number;
  strokeWidth?: number;
}

export const DoughnutChart: React.FC<DoughnutChartProps> = ({ data, size = 120, strokeWidth = 15 }) => {
  const halfsize = size / 2;
  const radius = halfsize - strokeWidth / 2;
  const circumference = 2 * Math.PI * radius;
  const total = data.reduce((acc, segment) => acc + segment.value, 0);

  let accumulatedPercent = 0;
  
  if (total === 0) {
    return (
        <div style={{ width: size, height: size }} className="relative flex items-center justify-center">
            <svg width={size} height={size} viewBox={`0 0 ${size} ${size}`}>
                <circle
                    cx={halfsize}
                    cy={halfsize}
                    r={radius}
                    stroke="#E5E7EB"
                    strokeWidth={strokeWidth}
                    fill="none"
                />
            </svg>
            <div className="absolute flex flex-col items-center justify-center">
                <span className="text-gray-500 text-sm">N/A</span>
            </div>
        </div>
    );
  }

  return (
    <div style={{ width: size, height: size }} className="relative">
      <svg width={size} height={size} viewBox={`0 0 ${size} ${size}`} className="-rotate-90">
        <g>
            {/* Background circle */}
            <circle
                cx={halfsize}
                cy={halfsize}
                r={radius}
                stroke="#E5E7EB"
                strokeWidth={strokeWidth}
                fill="none"
            />
            {data.map((segment, index) => {
            const percent = (segment.value / total) * 100;
            const strokeDasharray = `${(circumference * percent) / 100} ${circumference}`;
            const rotation = accumulatedPercent * 3.6;
            accumulatedPercent += percent;

            return (
                <circle
                key={index}
                cx={halfsize}
                cy={halfsize}
                r={radius}
                stroke={segment.color}
                strokeWidth={strokeWidth}
                fill="none"
                strokeDasharray={strokeDasharray}
                strokeLinecap="round"
                transform={`rotate(${rotation}, ${halfsize}, ${halfsize})`}
                />
            );
            })}
        </g>
      </svg>
      <div className="absolute inset-0 flex flex-col items-center justify-center">
        <span className="text-2xl font-bold text-gray-800">{total}</span>
        <span className="text-xs text-gray-500">Absences</span>
      </div>
    </div>
  );
};
