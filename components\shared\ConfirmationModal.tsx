import React from 'react';
import { Icon } from './Icon';
import { Spinner } from './Spinner';

interface ConfirmationModalProps {
  isOpen: boolean;
  onClose: () => void;
  onConfirm: () => void;
  title: string;
  message: React.ReactNode;
  confirmText?: string;
  cancelText?: string;
  isConfirming?: boolean;
}

export const ConfirmationModal: React.FC<ConfirmationModalProps> = ({
  isOpen,
  onClose,
  onConfirm,
  title,
  message,
  confirmText = 'Confirmer',
  cancelText = 'Annuler',
  isConfirming = false,
}) => {
  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 bg-black bg-opacity-60 flex justify-center items-center z-[999] p-4" aria-modal="true" role="dialog">
      <div className="bg-white rounded-xl shadow-2xl w-full max-w-md p-6 m-4 modal-content-animate">
        <div className="text-center">
            <div className="mx-auto flex items-center justify-center h-12 w-12 rounded-full bg-red-100 mb-4">
                <Icon.Warning className="h-6 w-6 text-red-600" />
            </div>
            <h3 className="text-lg leading-6 font-bold text-gray-900" id="modal-title">
                {title}
            </h3>
            <div className="mt-2">
                <div className="text-sm text-gray-600 space-y-2">
                    {message}
                </div>
            </div>
        </div>
        <div className="mt-6 flex flex-col-reverse sm:flex-row-reverse gap-3">
          <button
            type="button"
            onClick={onConfirm}
            disabled={isConfirming}
            className="w-full inline-flex justify-center rounded-lg border border-transparent shadow-sm px-4 py-2 bg-red-600 text-base font-medium text-white hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500 sm:text-sm disabled:bg-red-300 disabled:cursor-not-allowed"
          >
            {isConfirming ? <Spinner size="sm" color="white" /> : confirmText}
          </button>
          <button
            type="button"
            onClick={onClose}
            disabled={isConfirming}
            className="w-full inline-flex justify-center rounded-lg border border-gray-300 shadow-sm px-4 py-2 bg-white text-base font-medium text-gray-700 hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-[--org-color-main] sm:mt-0 sm:text-sm"
          >
            {cancelText}
          </button>
        </div>
      </div>
    </div>
  );
};