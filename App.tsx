import React, { useState, useEffect, useCallback } from 'react';
import { Dashboard } from './components/Dashboard';
import { DocumentLibrary } from './components/DocumentLibrary';
import { EmployeesView } from './components/EmployeesView';
import { Icon } from './components/shared/Icon';
import { View, Employee, HRDocument, PayrollReport, PayrollAnalysis, Organization, Absence, ReportSummary, Anomaly, DocumentCategory, MonthlyAbsenceReport, CandidateAudit } from './types';
import { Login } from './components/Login';
import { SignUp } from './components/SignUp';
import { AddEmployeeModal } from './components/AddEmployeeModal';
import { analyzeDocumentContent, analyzeMedicalCertificate, getDocumentCategory, MedicalCertificateAnalysis, analyzeCandidateCV } from './services/geminiService';
import { supabase } from './services/supabaseClient';
import { Spinner } from './components/shared/Spinner';
import type { Session, PostgrestError } from '@supabase/supabase-js';
import type { Database } from './services/supabaseClient';
import { OrganizationSelector } from './components/OrganizationSelector';
import Notification from './components/shared/Notification';
import { useNotification } from './hooks/useNotification';
import { PayrollView } from './components/PayrollView';
import { ComplianceAnalysisModal } from './components/ComplianceAnalysisModal';
import { AddOrganizationModal } from './components/AddOrganizationModal';
import { EmployeeProfileView } from './components/EmployeeProfileView';
import { getErrorMessage } from './services/errorService';
import { DiplomaAnalysisModal } from './components/DiplomaAnalysisModal';
import { generateColorFromString } from './services/colorService';
import { AddAbsenceModal } from './components/AddAbsenceModal';
import { generateAbsenceReport } from './services/reportService';
import { ReportsView } from './components/ReportsView';
import { WorkCertificateAnalysisModal } from './components/WorkCertificateAnalysisModal';
import { MonthlyArchiveModal } from './components/MonthlyArchiveModal';
import { BatchDocumentModal } from './components/BatchDocumentModal';
import { ConfirmationModal } from './components/shared/ConfirmationModal';
import { CandidateAuditView } from './components/CandidateAuditView';
import { AuditReportModal } from './components/AuditReportModal';
import { ServerErrorModal } from './components/shared/ServerErrorModal';

// --- SQL Scripts for Database Setup ---

const candidateAuditsSql = `-- 1. Create the table for Candidate Audits
CREATE TABLE public.candidate_audits (
    id uuid NOT NULL DEFAULT gen_random_uuid(),
    created_at timestamp with time zone NOT NULL DEFAULT now(),
    organization_id uuid NOT NULL,
    user_id uuid NOT NULL,
    candidate_name text NOT NULL,
    source_file_name text NOT NULL,
    storage_path text NOT NULL,
    status text NOT NULL CHECK (status IN ('Conforme', 'Non Conforme', 'En cours', 'Échec')),
    analysis_report jsonb NULL,
    CONSTRAINT candidate_audits_pkey PRIMARY KEY (id),
    CONSTRAINT candidate_audits_organization_id_fkey FOREIGN KEY (organization_id) REFERENCES organizations(id) ON DELETE CASCADE,
    CONSTRAINT candidate_audits_user_id_fkey FOREIGN KEY (user_id) REFERENCES auth.users(id) ON DELETE CASCADE
);

-- 2. Enable Row Level Security (RLS)
ALTER TABLE public.candidate_audits ENABLE ROW LEVEL SECURITY;

-- 3. Create RLS policies
CREATE POLICY "Allow select for org members" ON public.candidate_audits FOR SELECT USING (EXISTS (SELECT 1 FROM organizations WHERE organizations.id = candidate_audits.organization_id AND organizations.user_id = auth.uid()));
CREATE POLICY "Allow insert for org members" ON public.candidate_audits FOR INSERT WITH CHECK (EXISTS (SELECT 1 FROM organizations WHERE organizations.id = candidate_audits.organization_id AND organizations.user_id = auth.uid()));
CREATE POLICY "Allow update for org members" ON public.candidate_audits FOR UPDATE USING (EXISTS (SELECT 1 FROM organizations WHERE organizations.id = candidate_audits.organization_id AND organizations.user_id = auth.uid()));
CREATE POLICY "Allow delete for org members" ON public.candidate_audits FOR DELETE USING (EXISTS (SELECT 1 FROM organizations WHERE organizations.id = candidate_audits.organization_id AND organizations.user_id = auth.uid()));

-- 4. Add comment on the table for clarity
COMMENT ON TABLE public.candidate_audits IS 'Stores results of candidate CV audits.';`;

const monthlyAbsenceReportsSql = `-- 1. Create the table for Monthly Absence Reports
CREATE TABLE public.monthly_absence_reports (
    id uuid NOT NULL DEFAULT gen_random_uuid(),
    created_at timestamp with time zone NOT NULL DEFAULT now(),
    organization_id uuid NOT NULL,
    user_id uuid NOT NULL,
    report_period text NOT NULL,
    month integer NOT NULL,
    year integer NOT NULL,
    report_data jsonb NOT NULL,
    CONSTRAINT monthly_absence_reports_pkey PRIMARY KEY (id),
    CONSTRAINT monthly_absence_reports_organization_id_fkey FOREIGN KEY (organization_id) REFERENCES organizations(id) ON DELETE CASCADE,
    CONSTRAINT monthly_absence_reports_user_id_fkey FOREIGN KEY (user_id) REFERENCES auth.users(id) ON DELETE CASCADE
);

-- 2. Enable RLS
ALTER TABLE public.monthly_absence_reports ENABLE ROW LEVEL SECURITY;

-- 3. RLS Policies
CREATE POLICY "Allow all for org members" ON public.monthly_absence_reports FOR ALL USING (EXISTS (SELECT 1 FROM organizations WHERE organizations.id = monthly_absence_reports.organization_id AND organizations.user_id = auth.uid()));

-- 4. Add comment
COMMENT ON TABLE public.monthly_absence_reports IS 'Stores archived monthly absence reports.';`;

const absencesTableSql = `-- Add AI analysis columns to the absences table if they don't exist.
-- Run this script to ensure your 'absences' table is up-to-date for AI features.
ALTER TABLE public.absences
ADD COLUMN IF NOT EXISTS ocr_text text NULL,
ADD COLUMN IF NOT EXISTS doctor_name text NULL,
ADD COLUMN IF NOT EXISTS extracted_reason text NULL,
ADD COLUMN IF NOT EXISTS extracted_start_date text NULL,
ADD COLUMN IF NOT EXISTS extracted_end_date text NULL,
ADD COLUMN IF NOT EXISTS extracted_patient_name text NULL,
ADD COLUMN IF NOT EXISTS stamp_detected boolean NULL;`;


// Use derived types from the central Database definition
type OrganizationRow = Database['public']['Tables']['organizations']['Row'];

type EmployeeRow = Database['public']['Tables']['employees']['Row'];
type EmployeeInsert = Database['public']['Tables']['employees']['Insert'];

type HrDocumentRow = Database['public']['Tables']['hr_documents']['Row'];
type HrDocumentInsert = Database['public']['Tables']['hr_documents']['Insert'];
type HrDocumentUpdate = Database['public']['Tables']['hr_documents']['Update'];

type PayrollReportRow = Database['public']['Tables']['payroll_reports']['Row'];
type PayrollReportInsert = Database['public']['Tables']['payroll_reports']['Insert'];

type AbsenceRow = Database['public']['Tables']['absences']['Row'];
type AbsenceInsert = Database['public']['Tables']['absences']['Insert'];
type MonthlyAbsenceReportRow = Database['public']['Tables']['monthly_absence_reports']['Row'];
type MonthlyAbsenceReportInsert = Database['public']['Tables']['monthly_absence_reports']['Insert'];

type CandidateAuditRow = Database['public']['Tables']['candidate_audits']['Row'];
type CandidateAuditInsert = Database['public']['Tables']['candidate_audits']['Insert'];
type CandidateAuditUpdate = Database['public']['Tables']['candidate_audits']['Update'];


const mapAbsenceFromRow = (row: AbsenceRow): Absence => ({
    id: row.id,
    employeeId: row.employee_id,
    startDate: row.start_date,
    endDate: row.end_date,
    reason: row.reason,
    justified: row.justified,
    organizationId: row.organization_id,
    attachmentUrl: row.attachment_url,
    doctorName: row.doctor_name,
    stampDetected: row.stamp_detected || undefined,
});

export const App: React.FC = () => {
    const { showNotification } = useNotification();
    const [session, setSession] = useState<Session | null>(null);
    const [isAuthLoading, setIsAuthLoading] = useState(true);
    const [authView, setAuthView] = useState<'login' | 'signup'>('login');

    const [organizations, setOrganizations] = useState<Organization[]>([]);
    const [isOrgLoading, setIsOrgLoading] = useState(false);
    const [selectedOrganization, setSelectedOrganization] = useState<Organization | null>(null);
    const [showOrgModal, setShowOrgModal] = useState(false);
    const [orgToEdit, setOrgToEdit] = useState<Organization | null>(null);

    const [employees, setEmployees] = useState<Employee[]>([]);
    const [documents, setDocuments] = useState<HRDocument[]>([]);
    const [absences, setAbsences] = useState<AbsenceRow[]>([]);
    const [payrollReports, setPayrollReports] = useState<PayrollReport[]>([]);
    const [monthlyAbsenceReports, setMonthlyAbsenceReports] = useState<MonthlyAbsenceReport[]>([]);
    const [candidateAudits, setCandidateAudits] = useState<CandidateAudit[]>([]);
    
    const [isAppLoading, setIsAppLoading] = useState(false);
    const [currentView, setCurrentView] = useState<View>(View.Dashboard);

    const [selectedEmployee, setSelectedEmployee] = useState<Employee | null>(null);
    const [showEmployeeModal, setShowEmployeeModal] = useState(false);
    const [employeeToEdit, setEmployeeToEdit] = useState<Employee | null>(null);

    const [showAbsenceModal, setShowAbsenceModal] = useState(false);
    
    const [absenceSummary, setAbsenceSummary] = useState<ReportSummary | null>(null);
    const [absenceAnomalies, setAbsenceAnomalies] = useState<Anomaly[]>([]);
    
    const [analyzingDocId, setAnalyzingDocId] = useState<string | null>(null);
    const [docToAnalyze, setDocToAnalyze] = useState<HRDocument | null>(null);
    const [analysisModalType, setAnalysisModalType] = useState<'compliance' | 'diploma' | 'work_certificate' | null>(null);
    
    const [auditToView, setAuditToView] = useState<CandidateAudit | null>(null);
    
    const [storageBucketError, setStorageBucketError] = useState<string | null>(null);

    const [showBatchUploadModal, setShowBatchUploadModal] = useState(false);
    const [employeeForBatchUpload, setEmployeeForBatchUpload] = useState<Employee | null>(null);
    
    const [isArchiveFeatureAvailable, setIsArchiveFeatureAvailable] = useState(true);
    const [isCandidateAuditFeatureAvailable, setIsCandidateAuditFeatureAvailable] = useState(true);

    const [confirmation, setConfirmation] = useState<{ title: string; message: React.ReactNode; onConfirm: () => void; } | null>(null);
    const [isConfirming, setIsConfirming] = useState(false);
    
    const [serverErrorModalContent, setServerErrorModalContent] = useState<{ title: string; message: React.ReactNode; sqlScript: string; } | null>(null);


    // --- AUTH ---
    useEffect(() => {
        supabase.auth.getSession().then(({ data: { session } }) => {
            setSession(session);
            setIsAuthLoading(false);
        });

        const { data: { subscription } } = supabase.auth.onAuthStateChange((_event, session) => {
            setSession(session);
            if (!session) {
                setSelectedOrganization(null);
                setOrganizations([]);
            }
        });

        return () => subscription.unsubscribe();
    }, []);

    // --- ORGANIZATIONS ---
    const fetchUserOrganizations = useCallback(async () => {
        if (!session) return;
        setIsOrgLoading(true);
        const { data, error } = await supabase.from('organizations').select('*').eq('user_id', session.user.id);
        if (error) {
            showNotification(`Erreur: ${error.message}`, 'error');
        } else if (data) {
            setOrganizations(data as Organization[]);
        }
        setIsOrgLoading(false);
    }, [session, showNotification]);

    useEffect(() => {
        if (session) {
            fetchUserOrganizations();
        }
    }, [session, fetchUserOrganizations]);

    const handleSelectOrganization = (org: Organization) => {
        setSelectedOrganization(org);
        const { main, dark, light } = generateColorFromString(org.id);
        document.documentElement.style.setProperty('--org-color-main', main);
        document.documentElement.style.setProperty('--org-color-dark', dark);
        document.documentElement.style.setProperty('--org-color-light', light);
    };
    
    const handleAddOrganization = async (name: string) => {
        if (!session) return;
        const { data, error } = await supabase.from('organizations').insert({ name, user_id: session.user.id }).select();
        if (error) showNotification(`Erreur: ${error.message}`, 'error');
        else if (data) {
            showNotification('Organisation ajoutée avec succès!', 'success');
            await fetchUserOrganizations();
        }
        setShowOrgModal(false);
    };
    
    const handleUpdateOrganization = async (orgData: { id: string; name: string; }) => {
        const { error } = await supabase.from('organizations').update({ name: orgData.name }).eq('id', orgData.id);
        if (error) showNotification(`Erreur: ${error.message}`, 'error');
        else {
            showNotification('Organisation mise à jour!', 'success');
            await fetchUserOrganizations();
        }
        setShowOrgModal(false);
        setOrgToEdit(null);
    };

    const handleRequestDeleteOrganization = (org: Organization) => {
        setConfirmation({
            title: `Supprimer l'organisation "${org.name}"`,
            message: (
                <>
                    <p>Êtes-vous sûr de vouloir supprimer cette organisation ?</p>
                    <p className="font-bold text-red-600 mt-2">ATTENTION : Cette action est irréversible et supprimera toutes les données associées (employés, documents, etc.).</p>
                </>
            ),
            onConfirm: () => confirmDeleteOrganization(org.id),
        });
    };
    
    const confirmDeleteOrganization = async (organizationId: string) => {
        setIsConfirming(true);
        const { error } = await supabase.rpc('delete_organization_and_data', { org_id: organizationId });
        if (error) {
            showNotification(`Erreur lors de la suppression : ${error.message}`, 'error');
        } else {
            showNotification('Organisation et toutes ses données supprimées.', 'success');
            await fetchUserOrganizations();
            if (selectedOrganization?.id === organizationId) {
                setSelectedOrganization(null);
            }
        }
        setIsConfirming(false);
        setConfirmation(null);
    };
    
    const handleLogout = async () => {
        await supabase.auth.signOut();
        setSelectedOrganization(null);
    };
    
    // --- DATA FETCHING PER ORG ---
    const fetchDataForOrg = useCallback(async () => {
        if (!selectedOrganization) return;
        setIsAppLoading(true);
        setServerErrorModalContent(null); // Reset errors on new data fetch

        const [employeesRes, documentsRes, absencesRes, payrollReportsRes, monthlyAbsenceReportsRes, candidateAuditsRes] = await Promise.all([
            supabase.from('employees').select('*').eq('organization_id', selectedOrganization.id),
            supabase.from('hr_documents').select('*').eq('organization_id', selectedOrganization.id),
            supabase.from('absences').select('*').eq('organization_id', selectedOrganization.id),
            supabase.from('payroll_reports').select('*').eq('organization_id', selectedOrganization.id).order('created_at', { ascending: false }),
            supabase.from('monthly_absence_reports').select('*').eq('organization_id', selectedOrganization.id).order('year', { ascending: false }).order('month', { ascending: false }),
            supabase.from('candidate_audits').select('*').eq('organization_id', selectedOrganization.id).order('created_at', { ascending: false }),
        ]);

        if (employeesRes.error) showNotification(`Erreur employés: ${employeesRes.error.message}`, 'error');
        else if (employeesRes.data) {
            setEmployees(employeesRes.data.map((e: EmployeeRow) => ({
                id: e.id,
                name: e.name,
                position: e.position,
                team: e.team,
                organizationId: e.organization_id,
                contractStartDate: e.contract_start_date,
                contractEndDate: e.contract_end_date,
            })));
        }

        if (documentsRes.error) showNotification(`Erreur documents: ${documentsRes.error.message}`, 'error');
        else if (documentsRes.data) {
            const docsWithUrls: HRDocument[] = documentsRes.data.map((d: HrDocumentRow) => {
                const { data: { publicUrl } } = supabase.storage.from('hr_documents').getPublicUrl(d.storage_path);
                
                let normalizedCategory: DocumentCategory = d.category;
                const rawCategory = d.category as string;
        
                if (rawCategory && rawCategory.toLowerCase().replace(/ô/g, 'o') === 'diplome') {
                    normalizedCategory = 'Diplome';
                }
                
                return {
                    id: d.id,
                    name: d.name,
                    category: normalizedCategory,
                    uploadDate: d.upload_date,
                    storagePath: d.storage_path,
                    url: publicUrl,
                    organizationId: d.organization_id,
                    employeeId: d.employee_id,
                };
            });
            setDocuments(docsWithUrls);
        }

        if (absencesRes.error) showNotification(`Erreur absences: ${absencesRes.error.message}`, 'error');
        else if (absencesRes.data) setAbsences(absencesRes.data);

        if (payrollReportsRes.error) showNotification(`Erreur rapports paie: ${payrollReportsRes.error.message}`, 'error');
        else if (payrollReportsRes.data) setPayrollReports(payrollReportsRes.data as unknown as PayrollReport[]);
        
        // Handle missing 'candidate_audits' table
        if (candidateAuditsRes.error) {
            const errorMsg = candidateAuditsRes.error.message.toLowerCase();
            const isMissingTableError = candidateAuditsRes.error.code === '42P01' || errorMsg.includes('does not exist') || errorMsg.includes('could not find the table');
            
            if (isMissingTableError) {
                setIsCandidateAuditFeatureAvailable(false);
                if (!serverErrorModalContent) {
                     setServerErrorModalContent({
                        title: "Fonctionnalité d'Audit de Candidats Indisponible",
                        message: <p>La table <code>candidate_audits</code> est manquante dans votre base de données. Pour activer cette fonctionnalité, veuillez exécuter le script SQL suivant dans votre éditeur SQL Supabase.</p>,
                        sqlScript: candidateAuditsSql,
                    });
                }
            } else {
                 showNotification(`Erreur audits candidats: ${candidateAuditsRes.error.message}`, 'error');
            }
            setCandidateAudits([]);
        } else if (candidateAuditsRes.data) {
            setIsCandidateAuditFeatureAvailable(true);
            const auditsWithUrls = candidateAuditsRes.data.map((a: CandidateAuditRow) => {
                const { data: { publicUrl } } = supabase.storage.from('hr_documents').getPublicUrl(a.storage_path);
                return { ...a, analysis_report: a.analysis_report as any, url: publicUrl };
            });
            setCandidateAudits(auditsWithUrls as CandidateAudit[]);
        }
        
        // Handle missing 'monthly_absence_reports' table
        if (monthlyAbsenceReportsRes.error) {
            const errorMsg = monthlyAbsenceReportsRes.error.message.toLowerCase();
            const isMissingTableError = monthlyAbsenceReportsRes.error.code === '42P01' || errorMsg.includes('does not exist') || errorMsg.includes('could not find the table');

            if (isMissingTableError) {
                setIsArchiveFeatureAvailable(false);
                 if (!serverErrorModalContent) { // Show this error only if the candidate one isn't already showing
                    setServerErrorModalContent({
                        title: "Fonctionnalité d'Archivage Indisponible",
                        message: <p>La table <code>monthly_absence_reports</code> est manquante dans votre base de données. Pour activer l'archivage des rapports d'absence, veuillez exécuter le script SQL suivant.</p>,
                        sqlScript: monthlyAbsenceReportsSql,
                    });
                }
            } else {
                setIsArchiveFeatureAvailable(true);
                showNotification(`Erreur rapports mensuels: ${monthlyAbsenceReportsRes.error.message}`, 'error');
            }
            setMonthlyAbsenceReports([]);
        } else if (monthlyAbsenceReportsRes.data) {
            setIsArchiveFeatureAvailable(true);
            setMonthlyAbsenceReports(monthlyAbsenceReportsRes.data as unknown as MonthlyAbsenceReport[]);
        }

        setIsAppLoading(false);
    }, [selectedOrganization, showNotification, serverErrorModalContent]);

    useEffect(() => {
        if (selectedOrganization) {
            fetchDataForOrg();
        }
    }, [selectedOrganization, fetchDataForOrg]);

    const checkForStorageBucket = useCallback(async () => {
        const { error } = await supabase.storage.from('hr_documents').upload('check.txt', new Blob(['check']), { upsert: true });
        if (error && error.message.toLowerCase().includes('bucket not found')) {
            setStorageBucketError("Erreur de Configuration Requise : Le 'bucket' de stockage 'hr_documents' est introuvable. Veuillez le créer dans votre projet Supabase.");
        } else {
            setStorageBucketError(null);
            await supabase.storage.from('hr_documents').remove(['check.txt']);
        }
    }, [supabase.storage]);

    useEffect(() => {
        checkForStorageBucket();
    }, [checkForStorageBucket]);
    
    // --- ABSENCE REPORTING ---
    useEffect(() => {
        console.log("runReport useEffect triggered");
        const runReport = async () => {
            if (employees.length > 0 && absences.length > 0) {
                 try {
                    console.log("Calling generateAbsenceReport");
                    const mappedAbsences = absences.map(mapAbsenceFromRow);
                    const { summary, anomalies } = await generateAbsenceReport(mappedAbsences, employees);
                    console.log("generateAbsenceReport success", { summary, anomalies });
                    setAbsenceSummary(summary);
                    setAbsenceAnomalies(anomalies);
                 } catch (error) {
                    console.error("Error in runReport", error);
                    showNotification(getErrorMessage(error), 'error');
                    setAbsenceAnomalies([]); // Clear anomalies on error
                 }
            } else if (absences.length > 0) {
                 console.log("runReport: absences but no employees");
                 setAbsenceSummary({ total: absences.length, justified: absences.filter(a => a.justified).length, unjustified: absences.filter(a => !a.justified).length, anomaliesCount: 0 });
                 setAbsenceAnomalies([]);
            } else {
                 console.log("runReport: no absences");
                 setAbsenceSummary(null);
                 setAbsenceAnomalies([]);
            }
        };
        runReport();
    }, [absences, employees, showNotification]);
    
    // --- EMPLOYEES ---
    const handleAddEmployee = async (employeeData: Omit<Employee, 'id' | 'organizationId'>) => {
        if (!selectedOrganization) return;
        const { name, position, team, contractStartDate, contractEndDate } = employeeData;
        const insertPayload: EmployeeInsert = {
            name,
            position,
            team,
            organization_id: selectedOrganization.id,
            contract_start_date: contractStartDate,
            contract_end_date: contractEndDate,
        };
        const { error } = await supabase.from('employees').insert(insertPayload);
        if (error) showNotification(getErrorMessage(error), 'error');
        else {
            showNotification("Employé ajouté!", 'success');
            fetchDataForOrg();
        }
        setShowEmployeeModal(false);
    };

    const handleUpdateEmployee = async (employeeData: Employee) => {
        const { name, position, team, contractStartDate, contractEndDate } = employeeData;
        const updatePayload = {
            name,
            position,
            team,
            contract_start_date: contractStartDate,
            contract_end_date: contractEndDate,
        };
        const { error } = await supabase.from('employees').update(updatePayload).eq('id', employeeData.id);
        if (error) showNotification(getErrorMessage(error), 'error');
        else {
            showNotification("Employé mis à jour!", 'success');
            fetchDataForOrg();
        }
        setShowEmployeeModal(false);
        setEmployeeToEdit(null);
    };

    // --- DOCUMENTS ---
    const addDocument = async (
        docData: Omit<HRDocument, 'id' | 'uploadDate' | 'url' | 'storagePath' | 'organizationId'> & { file: File },
        options: { showNotifications?: boolean, fetchData?: boolean } = { showNotifications: true, fetchData: true }
    ) => {
        if (!selectedOrganization) return;

        const { file, name, category, employeeId } = docData;
        const filePath = `${selectedOrganization.id}/${Date.now()}_${file.name}`;
        
        try {
            const { error: uploadError } = await supabase.storage.from('hr_documents').upload(filePath, file);
            if (uploadError) {
                if (getErrorMessage(uploadError).toLowerCase().includes('bucket not found')) {
                    setStorageBucketError("Action Requise: Le 'storage bucket' (nommé 'hr_documents') est introuvable. Veuillez le créer dans votre projet Supabase avec un accès public.");
                }
                throw uploadError;
            }
            setStorageBucketError(null);
            
            const dbRecord: HrDocumentInsert = { name, category, storage_path: filePath, organization_id: selectedOrganization.id, employee_id: employeeId || null };

            let { error: dbError } = await supabase.from('hr_documents').insert(dbRecord);

            if (dbError) {
                // Use the specific error code for invalid enum value for robustness.
                const isEnumError = dbError.code === '22P02' && dbError.message.toLowerCase().includes('document_category');

                if (isEnumError) {
                    if (options.showNotifications) {
                        showNotification(`Catégorie "${category}" invalide. Sauvegardé comme "Contrat".`, 'info', 7000);
                    }
                    const fallbackRecord: HrDocumentInsert = { ...dbRecord, category: 'Contrat' };
                    const { error: fallbackError } = await supabase.from('hr_documents').insert(fallbackRecord);
                    
                    if (fallbackError) throw fallbackError;
                    dbError = null; 
                } else {
                    throw dbError;
                }
            }
            
            if (!dbError) {
                if (options.showNotifications) showNotification("Document ajouté!", 'success');
                if (options.fetchData) await fetchDataForOrg();
            }

        } catch (error) {
            // The calling components will handle showing notifications for thrown errors.
            throw error;
        }
    };

    const handleBatchUpload = async (files: File[], employeeId: string): Promise<{ success: boolean; name: string; error?: string }[]> => {
        if (!selectedOrganization) return [];

        const results = [];
        for (const file of files) {
            try {
                const category = await getDocumentCategory(file);
                await addDocument(
                    { file, name: file.name, category, employeeId },
                    { showNotifications: false, fetchData: false }
                );
                results.push({ success: true, name: file.name });
            } catch (error) {
                const errorMessage = getErrorMessage(error);
                results.push({ success: false, name: file.name, error: errorMessage });
            }
        }
        
        const successes = results.filter(r => r.success).length;
        const failures = results.length - successes;
        
        if (successes > 0 || failures > 0) {
             if (failures > 0) {
                showNotification(`${successes} document(s) ajouté(s). ${failures} échec(s).`, successes > 0 ? 'info' : 'error');
            } else {
                showNotification(`${successes} document(s) ajouté(s) avec succès !`, 'success');
            }
        }
        
        if (successes > 0) {
            await fetchDataForOrg();
        }

        return results;
    };

    const updateDocument = async (
        docData: Omit<HRDocument, 'uploadDate' | 'url'> & { file?: File },
        options: { showNotifications?: boolean, fetchData?: boolean } = { showNotifications: true, fetchData: true }
    ) => {
        const { file, id, name, category, employeeId, storagePath, organizationId } = docData;
        
        try {
            let newStoragePath = storagePath;

            if (file) {
                const filePath = `${organizationId}/${Date.now()}_${file.name}`;
                const { error: uploadError } = await supabase.storage.from('hr_documents').upload(filePath, file);
                if (uploadError) throw uploadError;
                
                newStoragePath = filePath;
                if (storagePath) {
                    await supabase.storage.from('hr_documents').remove([storagePath]);
                }
            }
            
            const updatePayload: HrDocumentUpdate = { name, category, employee_id: employeeId || null, storage_path: newStoragePath };
            let { error: dbError } = await supabase.from('hr_documents').update(updatePayload).eq('id', id);

            if (dbError) {
                const isEnumError = dbError.code === '22P02' && dbError.message.toLowerCase().includes('document_category');

                if (isEnumError) {
                    if (options.showNotifications) {
                        showNotification(`Catégorie "${category}" invalide. Mise à jour comme "Contrat".`, 'info', 7000);
                    }
                    const fallbackPayload: HrDocumentUpdate = { ...updatePayload, category: 'Contrat' };
                    const { error: fallbackError } = await supabase.from('hr_documents').update(fallbackPayload).eq('id', id);

                    if (fallbackError) throw fallbackError;
                    dbError = null;
                } else {
                    throw dbError;
                }
            }

            if (!dbError) {
                if (options.showNotifications) showNotification("Document mis à jour!", 'success');
                if (options.fetchData) await fetchDataForOrg();
            }
        } catch (error) {
            throw error;
        }
    };

    const handleDeleteDocument = (docToDelete: HRDocument) => {
        setConfirmation({
            title: 'Confirmer la suppression',
            message: <p>Êtes-vous sûr de vouloir supprimer définitivement le document <strong>"{docToDelete.name}"</strong> ? Cette action est irréversible.</p>,
            onConfirm: () => confirmDeleteDocument(docToDelete),
        });
    };

    const confirmDeleteDocument = async (docToDelete: HRDocument) => {
        setIsConfirming(true);
        setDocuments(prev => prev.filter(d => d.id !== docToDelete.id));

        try {
            const { error: storageError } = await supabase.storage.from('hr_documents').remove([docToDelete.storagePath]);
            if (storageError && !storageError.message.toLowerCase().includes('not found')) {
                 throw storageError;
            }
            const { error: dbError } = await supabase.from('hr_documents').delete().eq('id', docToDelete.id);
            if (dbError) throw dbError;
            showNotification("Document supprimé avec succès.", 'success');
        } catch (error) {
            const errorMessage = getErrorMessage(error);
            if (errorMessage.toLowerCase().includes('rls') || errorMessage.toLowerCase().includes('policy')) {
                 showNotification(`Erreur de permissions. Vérifiez les politiques RLS pour la suppression sur 'hr_documents' et sur le bucket de stockage.`, 'error', 15000);
            } else {
                showNotification(`Erreur de suppression : ${errorMessage}`, 'error', 10000);
            }
            fetchDataForOrg(); 
        } finally {
            setIsConfirming(false);
            setConfirmation(null);
        }
    };
    
    const handleAnalyzeDocument = async (doc: HRDocument) => {
        setAnalyzingDocId(doc.id);
        setDocToAnalyze(null);
        try {
            const { publicUrl } = supabase.storage.from('hr_documents').getPublicUrl(doc.storagePath).data;
            const response = await fetch(publicUrl);
            const blob = await response.blob();
            const file = new File([blob], doc.name, { type: blob.type });

            const analysis = await analyzeDocumentContent(file, doc.category);
            
            const analyzedDoc = { ...doc, contentComplianceAnalysis: analysis };
            setDocToAnalyze(analyzedDoc);
            
            if (doc.category === 'Contrat' || doc.category === 'reglement_interne') {
                setAnalysisModalType('compliance');
            } else if (doc.category === 'Diplome') {
                setAnalysisModalType('diploma');
            } else if (doc.category === 'certificat_de_travail') {
                setAnalysisModalType('work_certificate');
            }

        } catch (error) {
            showNotification(getErrorMessage(error), 'error');
        } finally {
            setAnalyzingDocId(null);
        }
    };
    
    const handleOpenBatchUpload = (employee: Employee) => {
        setEmployeeForBatchUpload(employee);
        setShowBatchUploadModal(true);
    };
    
    // --- ABSENCES ---
    const handleAddAbsence = async (
        absenceData: Omit<AbsenceInsert, 'id' | 'organization_id' | 'employee_id' | 'attachment_url'>,
        file: File
    ) => {
        if (!selectedOrganization || !selectedEmployee) return;

        const filePath = `${selectedOrganization.id}/absences/${selectedEmployee.id}/${Date.now()}_${file.name}`;
        const { error: uploadError } = await supabase.storage.from('hr_documents').upload(filePath, file);

        if (uploadError) {
            const errorMessage = getErrorMessage(uploadError);
            if (errorMessage.toLowerCase().includes('bucket not found')) {
                setStorageBucketError("Action Requise: Le bucket 'hr_documents' est manquant. Créez-le dans Supabase.");
                showNotification("Erreur de Configuration : Le bucket de stockage est manquant.", 'error');
            } else {
                showNotification(`Erreur de téléversement : ${errorMessage}`, 'error');
            }
            return;
        }
        setStorageBucketError(null);
        const { publicUrl } = supabase.storage.from('hr_documents').getPublicUrl(filePath).data;

        const coreData: AbsenceInsert = {
            employee_id: selectedEmployee.id,
            organization_id: selectedOrganization.id,
            start_date: absenceData.start_date,
            end_date: absenceData.end_date,
            reason: absenceData.reason,
            justified: absenceData.justified,
            attachment_url: publicUrl,
        };

        const { data: newAbsence, error: insertError } = await supabase
            .from('absences')
            .insert(coreData)
            .select('id')
            .single();

        if (insertError || !newAbsence) {
            showNotification(`Erreur d'enregistrement : ${getErrorMessage(insertError)}`, 'error');
            await supabase.storage.from('hr_documents').remove([filePath]);
            return;
        }
        
        const aiAnalysisData = {
            doctor_name: absenceData.doctor_name,
            stamp_detected: absenceData.stamp_detected,
            extracted_reason: absenceData.extracted_reason,
            extracted_start_date: absenceData.extracted_start_date,
            extracted_end_date: absenceData.extracted_end_date,
            extracted_patient_name: absenceData.extracted_patient_name,
            ocr_text: absenceData.ocr_text,
        };

        const { error: updateError } = await supabase
            .from('absences')
            .update(aiAnalysisData)
            .eq('id', newAbsence.id);

        const errorMessage = updateError ? getErrorMessage(updateError) : '';
        const isMissingColumnError = errorMessage.toLowerCase().includes('column') && errorMessage.toLowerCase().includes('does not exist');

        if (isMissingColumnError) {
             setServerErrorModalContent({
                title: "Mise à Jour de la Table 'absences' Requise",
                message: (
                    <>
                        <p>L'enregistrement de l'absence a réussi, mais la sauvegarde des résultats de l'analyse IA a échoué car votre table <code>absences</code> n'est pas à jour.</p>
                        <p className="mt-2">Veuillez exécuter le script SQL suivant pour ajouter les colonnes nécessaires sans perdre de données existantes.</p>
                    </>
                ),
                sqlScript: absencesTableSql
            });
        } else if (updateError) {
            showNotification(`Absence enregistrée, mais l'enregistrement de l'analyse IA a échoué. (${errorMessage})`, 'info', 8000);
        } else {
            showNotification("Absence enregistrée avec succès.", 'success');
        }


        await fetchDataForOrg(); 
        setShowAbsenceModal(false);
    };

    const handleAnalyzeCertificate = async (file: File): Promise<MedicalCertificateAnalysis> => {
        return analyzeMedicalCertificate(file);
    };

    const handleAddMonthlyAbsenceReport = async (month: number, year: number, periodLabel: string) => {
        if (!selectedOrganization || !session) return;
        
        const { data: existing, error: checkError } = await supabase
            .from('monthly_absence_reports')
            .select('id')
            .eq('organization_id', selectedOrganization.id)
            .eq('month', month)
            .eq('year', year)
            .maybeSingle();

        if (checkError) {
             showNotification(`Erreur lors de la vérification de l'archive: ${getErrorMessage(checkError)}`, 'error');
             return;
        }

        if (existing) {
            showNotification(`Un rapport pour ${periodLabel} existe déjà.`, 'info');
            return;
        }

        const firstDay = new Date(year, month - 1, 1).toISOString();
        const lastDay = new Date(year, month, 0).toISOString();
        
        const {data: monthlyAbsences, error: fetchError} = await supabase
            .from('absences')
            .select('*')
            .eq('organization_id', selectedOrganization.id)
            .gte('start_date', firstDay)
            .lte('start_date', lastDay);

        if (fetchError) {
            showNotification(`Erreur de récupération des absences: ${getErrorMessage(fetchError)}`, 'error');
            return;
        }
        
        if (!monthlyAbsences || monthlyAbsences.length === 0) {
            showNotification(`Aucune absence à rapporter pour ${periodLabel}.`, 'info');
            return;
        }

        const { summary, anomalies } = await generateAbsenceReport(monthlyAbsences.map(mapAbsenceFromRow), employees);
        const reportData = { summary, anomalies, absences: monthlyAbsences };

        const insertData: MonthlyAbsenceReportInsert = {
            organization_id: selectedOrganization.id,
            user_id: session.user.id,
            report_period: periodLabel,
            month: month,
            year: year,
            report_data: reportData as any,
        };

        const { error: insertError } = await supabase.from('monthly_absence_reports').insert(insertData);
        if (insertError) {
            showNotification(`Erreur de sauvegarde du rapport: ${getErrorMessage(insertError)}`, 'error');
        } else {
            showNotification(`Rapport pour ${periodLabel} archivé avec succès.`, 'success');
            fetchDataForOrg();
        }
    };
    
    const handleDeleteMonthlyAbsenceReport = (reportId: string) => {
        setConfirmation({
            title: "Supprimer le rapport archivé",
            message: <p>Êtes-vous sûr de vouloir supprimer ce rapport archivé ? Cette action est irréversible.</p>,
            onConfirm: () => confirmDeleteMonthlyAbsenceReport(reportId),
        });
    };

    const confirmDeleteMonthlyAbsenceReport = async (reportId: string) => {
        setIsConfirming(true);
        setMonthlyAbsenceReports(prev => prev.filter(r => r.id !== reportId));
        const { error } = await supabase.from('monthly_absence_reports').delete().eq('id', reportId);
        if (error) {
            showNotification(getErrorMessage(error), 'error');
            fetchDataForOrg();
        } else {
            showNotification("Rapport archivé supprimé.", 'success');
        }
        setIsConfirming(false);
        setConfirmation(null);
    };

    // --- PAYROLL ---
    const handleAddPayrollReport = async (reportData: Omit<PayrollReport, 'id' | 'created_at' | 'organization_id' | 'user_id'>) => {
        if (!selectedOrganization || !session) return;
        const insertData = { ...reportData, organization_id: selectedOrganization.id, user_id: session.user.id, analysis_results: reportData.analysis_results as any };
        const { error } = await supabase.from('payroll_reports').insert(insertData);
        if (error) showNotification(getErrorMessage(error), 'error');
        else {
            showNotification("Rapport de paie sauvegardé.", 'success');
            fetchDataForOrg();
        }
    };
    
    const handleDeletePayrollReport = (reportId: string) => {
        setConfirmation({
            title: "Supprimer le rapport de paie",
            message: <p>Êtes-vous sûr de vouloir supprimer ce rapport ? Cette action est irréversible.</p>,
            onConfirm: () => confirmDeletePayrollReport(reportId),
        });
    };

    const confirmDeletePayrollReport = async (reportId: string) => {
        setIsConfirming(true);
        setPayrollReports(prev => prev.filter(r => r.id !== reportId));
        
        const { error } = await supabase.from('payroll_reports').delete().eq('id', reportId);
        if (error) {
            showNotification(getErrorMessage(error), 'error');
            fetchDataForOrg();
        } else {
            showNotification("Rapport supprimé.", 'success');
        }
        setIsConfirming(false);
        setConfirmation(null);
    };

    // --- CANDIDATE AUDIT ---
    const handleAddCandidateAudit = async (file: File) => {
        if (!selectedOrganization || !session) return;

        const filePath = `${selectedOrganization.id}/cvs/${Date.now()}_${file.name}`;
        
        const tempId = `temp_${Date.now()}`;
        const tempAudit: CandidateAudit = {
            id: tempId,
            created_at: new Date().toISOString(),
            organization_id: selectedOrganization.id,
            user_id: session.user.id,
            candidate_name: 'Analyse en cours...',
            source_file_name: file.name,
            storage_path: filePath,
            url: URL.createObjectURL(file), 
            status: 'En cours',
            analysis_report: null,
        };

        setCandidateAudits(prev => [tempAudit, ...prev]);

        try {
            const { error: uploadError } = await supabase.storage.from('hr_documents').upload(filePath, file);
            if (uploadError) throw uploadError;

            const analysisReport = await analyzeCandidateCV(file);
            const candidateName = analysisReport.extractedData.name || 'Candidat inconnu';

            const insertData: CandidateAuditInsert = {
                organization_id: selectedOrganization.id,
                user_id: session.user.id,
                candidate_name: candidateName,
                source_file_name: file.name,
                storage_path: filePath,
                status: analysisReport.status,
                analysis_report: analysisReport as any,
            };
            
            const { data: newAudit, error: dbError } = await supabase.from('candidate_audits').insert(insertData).select().single();
            if (dbError) throw dbError;

            const { data: { publicUrl } } = supabase.storage.from('hr_documents').getPublicUrl(newAudit.storage_path);
            const finalAudit = { ...newAudit, url: publicUrl, analysis_report: newAudit.analysis_report as any };

            setCandidateAudits(prev => prev.map(a => a.id === tempId ? finalAudit : a));
            showNotification(`Analyse de ${candidateName} terminée.`, 'success');

        } catch (error) {
            const errorMessage = getErrorMessage(error);
            showNotification(`Erreur d'analyse : ${errorMessage}`, 'error');
            setCandidateAudits(prev => prev.filter(a => a.id !== tempId));
        }
    };

    const handleDeleteCandidateAudit = (audit: CandidateAudit) => {
        setConfirmation({
            title: "Supprimer l'audit du candidat",
            message: <p>Êtes-vous sûr de vouloir supprimer l'audit de <strong>{audit.candidate_name}</strong> ? Le CV et le rapport seront définitivement effacés.</p>,
            onConfirm: () => confirmDeleteCandidateAudit(audit),
        });
    };

    const confirmDeleteCandidateAudit = async (audit: CandidateAudit) => {
        setIsConfirming(true);
        setCandidateAudits(prev => prev.filter(a => a.id !== audit.id));

        try {
            await supabase.storage.from('hr_documents').remove([audit.storage_path]);
            await supabase.from('candidate_audits').delete().eq('id', audit.id);
            showNotification("Audit de candidat supprimé.", 'success');
        } catch (error) {
            showNotification(getErrorMessage(error), 'error');
            fetchDataForOrg();
        } finally {
            setIsConfirming(false);
            setConfirmation(null);
        }
    };


    // --- RENDER LOGIC ---
    if (isAuthLoading) {
        return <div className="flex items-center justify-center h-screen bg-[--lica-blue-light]"><Spinner size="lg" /></div>;
    }

    if (!session) {
        return authView === 'login' 
            ? <Login onSwitchToSignUp={() => setAuthView('signup')} /> 
            : <SignUp onSwitchToLogin={() => setAuthView('login')} />;
    }

    if (!selectedOrganization) {
        return (
            <>
                <OrganizationSelector
                    organizations={organizations}
                    isLoading={isOrgLoading}
                    onSelect={handleSelectOrganization}
                    onAdd={() => { setOrgToEdit(null); setShowOrgModal(true); }}
                    onEdit={(org) => { setOrgToEdit(org); setShowOrgModal(true); }}
                    onDelete={handleRequestDeleteOrganization}
                    onLogout={handleLogout}
                />
                {showOrgModal && (
                    <AddOrganizationModal 
                        onClose={() => setShowOrgModal(false)}
                        onAdd={handleAddOrganization}
                        onUpdate={handleUpdateOrganization}
                        organizationToEdit={orgToEdit || undefined}
                    />
                )}
                {confirmation && (
                    <ConfirmationModal
                        isOpen={!!confirmation}
                        onClose={() => setConfirmation(null)}
                        onConfirm={confirmation.onConfirm}
                        title={confirmation.title}
                        message={confirmation.message}
                        isConfirming={isConfirming}
                    />
                )}
            </>
        );
    }
    
    const renderView = () => {
        let viewToRender = currentView;
        if (viewToRender === View.CandidateAudit && !isCandidateAuditFeatureAvailable) {
            viewToRender = View.Dashboard;
        }

        switch (viewToRender) {
            case View.Dashboard:
                return <Dashboard employees={employees} absenceSummary={absenceSummary} absenceAnomalies={absenceAnomalies} onNavigateToReports={() => setCurrentView(View.Reports)} />;
            case View.Employees:
                return <EmployeesView employees={employees} onAddEmployee={() => { setEmployeeToEdit(null); setShowEmployeeModal(true); }} onSelectProfile={(emp) => { setSelectedEmployee(emp); setCurrentView(View.EmployeeProfile); }} onOpenBatchUpload={handleOpenBatchUpload} />;
            case View.Documents:
                return <DocumentLibrary documents={documents} employees={employees} addDocument={addDocument} updateDocument={updateDocument} deleteDocument={handleDeleteDocument} onAnalyze={handleAnalyzeDocument} analyzingDocId={analyzingDocId} storageBucketError={storageBucketError} />;
            case View.CandidateAudit:
                return <CandidateAuditView audits={candidateAudits} onAddAudit={handleAddCandidateAudit} onDeleteAudit={handleDeleteCandidateAudit} onSelectAudit={setAuditToView} />;
            case View.Payroll:
                return <PayrollView onAddReport={handleAddPayrollReport} onDeleteReport={handleDeletePayrollReport} savedReports={payrollReports} />;
            case View.Reports:
                return <ReportsView 
                            summary={absenceSummary} 
                            anomalies={absenceAnomalies} 
                            absences={absences} 
                            employees={employees}
                            archivedReports={monthlyAbsenceReports}
                            onArchive={handleAddMonthlyAbsenceReport}
                            onDeleteArchive={handleDeleteMonthlyAbsenceReport}
                            isArchiveFeatureAvailable={isArchiveFeatureAvailable}
                        />;
            case View.EmployeeProfile:
                if (selectedEmployee) {
                    return <EmployeeProfileView
                                employee={selectedEmployee}
                                absences={absences.filter(a => a.employee_id === selectedEmployee.id)}
                                onBack={() => { setSelectedEmployee(null); setCurrentView(View.Employees); }}
                                onEditEmployee={(emp) => { setEmployeeToEdit(emp); setShowEmployeeModal(true); }}
                                onAddAbsence={() => setShowAbsenceModal(true)}
                                storageBucketError={storageBucketError}
                            />;
                }
                return null;
            default:
                return <Dashboard employees={employees} absenceSummary={absenceSummary} absenceAnomalies={absenceAnomalies} onNavigateToReports={() => setCurrentView(View.Reports)} />;
        }
    };
    
    const navItems = [
        { view: View.Dashboard, label: 'Tableau de bord', icon: <Icon.Dashboard /> },
        { view: View.Employees, label: 'Employés', icon: <Icon.Users /> },
        { view: View.Documents, label: 'Documents RH', icon: <Icon.DocumentText /> },
        isCandidateAuditFeatureAvailable && { view: View.CandidateAudit, label: 'Audit Candidats', icon: <Icon.UserCircle /> },
        { view: View.Payroll, label: 'Analyse Paie', icon: <Icon.Clock /> },
        { view: View.Reports, label: 'Rapports Absence', icon: <Icon.Report /> },
    ].filter(Boolean) as { view: View; label: string; icon: JSX.Element; }[];

    return (
        <div className="flex h-screen bg-gray-50">
             <Notification />
            {/* Sidebar */}
            <aside className="w-64 bg-white shadow-md flex flex-col">
                <div className="p-4 border-b">
                    <button onClick={() => setSelectedOrganization(null)} className="w-full">
                       <Icon.LicaFullLogo className="h-12 w-auto text-[--org-color-main]" />
                       <h2 className="text-lg font-bold text-center mt-2 text-[--org-color-main] truncate">{selectedOrganization.name}</h2>
                    </button>
                </div>
                <nav className="flex-grow p-4 space-y-2">
                     {navItems.map(item => (
                        <button key={item.view} onClick={() => setCurrentView(item.view)} className={`w-full flex items-center p-3 rounded-lg text-sm font-semibold transition-colors ${currentView === item.view ? 'bg-[--org-color-light] text-[--org-color-main]' : 'text-gray-600 hover:bg-gray-100'}`}>
                            {React.cloneElement(item.icon, { className: 'h-5 w-5 mr-3' })}
                            {item.label}
                        </button>
                    ))}
                </nav>
                <div className="p-4 border-t">
                    <button onClick={handleLogout} className="w-full flex items-center p-3 rounded-lg text-sm font-semibold text-gray-600 hover:bg-red-50 hover:text-red-600 transition-colors">
                        <Icon.Logout className="h-5 w-5 mr-3" />
                        Se déconnecter
                    </button>
                </div>
            </aside>

            {/* Main Content */}
            <main className="flex-1 p-8 overflow-y-auto">
                {isAppLoading ? <div className="flex items-center justify-center h-full"><Spinner size="lg" /></div> : renderView()}
            </main>

            {/* Modals */}
            {serverErrorModalContent && (
                <ServerErrorModal
                    isOpen={!!serverErrorModalContent}
                    onClose={() => setServerErrorModalContent(null)}
                    title={serverErrorModalContent.title}
                    sqlScript={serverErrorModalContent.sqlScript}
                >
                    {serverErrorModalContent.message}
                </ServerErrorModal>
            )}
            {confirmation && (
                <ConfirmationModal
                    isOpen={!!confirmation}
                    onClose={() => setConfirmation(null)}
                    onConfirm={confirmation.onConfirm}
                    title={confirmation.title}
                    message={confirmation.message}
                    isConfirming={isConfirming}
                />
            )}
            {showEmployeeModal && (
                <AddEmployeeModal 
                    onClose={() => {setShowEmployeeModal(false); setEmployeeToEdit(null);}}
                    addEmployee={handleAddEmployee}
                    updateEmployee={handleUpdateEmployee}
                    employeeToEdit={employeeToEdit || undefined}
                />
            )}
            {showAbsenceModal && selectedEmployee && (
                <AddAbsenceModal
                    onClose={() => setShowAbsenceModal(false)}
                    employee={selectedEmployee}
                    onAddAbsence={handleAddAbsence}
                    analyzeCertificate={handleAnalyzeCertificate}
                />
            )}
            {docToAnalyze && analysisModalType === 'compliance' && (
                <ComplianceAnalysisModal document={docToAnalyze} onClose={() => setDocToAnalyze(null)} />
            )}
            {docToAnalyze && analysisModalType === 'diploma' && (
                <DiplomaAnalysisModal document={docToAnalyze} onClose={() => setDocToAnalyze(null)} />
            )}
            {docToAnalyze && analysisModalType === 'work_certificate' && (
                <WorkCertificateAnalysisModal document={docToAnalyze} onClose={() => setDocToAnalyze(null)} />
            )}
             {showBatchUploadModal && employeeForBatchUpload && (
                <BatchDocumentModal
                    employee={employeeForBatchUpload}
                    onClose={() => setShowBatchUploadModal(false)}
                    onBatchUpload={handleBatchUpload}
                />
            )}
            {auditToView && (
                <AuditReportModal audit={auditToView} onClose={() => setAuditToView(null)} />
            )}
        </div>
    );
};