
import React from 'react';

interface CardProps {
  children: React.ReactNode;
  className?: string;
}

export const Card: React.FC<CardProps> = ({ children, className = '' }) => {
  // Add card-hover for interactive effects, unless it's explicitly part of a non-interactive group (e.g. within another card)
  const isHoverable = !className.includes('non-hover');
  
  return (
    <div className={`bg-white border border-gray-200/80 rounded-xl shadow-sm ${isHoverable ? 'card-hover' : ''} ${className}`}>
      <div className={className.includes('p-') ? '' : 'p-6'}>
        {children}
      </div>
    </div>
  );
};