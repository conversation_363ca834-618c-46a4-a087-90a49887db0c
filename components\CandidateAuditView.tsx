import React, { useState, useMemo } from 'react';
import { CandidateAudit } from '../types';
import { Card } from './shared/Card';
import { Icon } from './shared/Icon';
import { Spinner } from './shared/Spinner';

interface CandidateAuditViewProps {
  audits: CandidateAudit[];
  onAddAudit: (file: File) => void;
  onDeleteAudit: (audit: CandidateAudit) => void;
  onSelectAudit: (audit: CandidateAudit) => void;
}

const getStatusBadge = (status: CandidateAudit['status']) => {
    switch (status) {
        case 'Conforme':
            return <span className="px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-green-100 text-green-800">Conforme</span>;
        case 'Non Conforme':
            return <span className="px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-red-100 text-red-800">Non Conforme</span>;
        case 'En cours':
            return <span className="px-2 inline-flex items-center text-xs leading-5 font-semibold rounded-full bg-blue-100 text-blue-800"><Spinner size="sm"/> <span className="ml-1">En cours</span></span>;
        case 'Échec':
             return <span className="px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-yellow-100 text-yellow-800">Échec</span>;
        default:
            return <span className="px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-gray-100 text-gray-800">Inconnu</span>;
    }
};

export const CandidateAuditView: React.FC<CandidateAuditViewProps> = ({ audits, onAddAudit, onDeleteAudit, onSelectAudit }) => {
    const [isDragOver, setIsDragOver] = useState(false);
    
    const handleFileSelect = (files: FileList | null) => {
        if (files) {
            Array.from(files).forEach(file => onAddAudit(file));
        }
    };

    const handleDrop = (e: React.DragEvent<HTMLDivElement>) => {
        e.preventDefault();
        e.stopPropagation();
        setIsDragOver(false);
        handleFileSelect(e.dataTransfer.files);
    };

    const sortedAudits = useMemo(() => {
        return [...audits].sort((a, b) => new Date(b.created_at).getTime() - new Date(a.created_at).getTime());
    }, [audits]);

    return (
        <div>
            <div className="flex justify-between items-center mb-8 pb-4 border-b border-gray-200">
                <h1 className="text-3xl font-bold text-[--org-color-main] uppercase tracking-wide">Audit & Validation de Candidatures</h1>
            </div>
            
            <Card className={`mb-8 transition-all duration-200 ${isDragOver ? 'border-[--org-color-main] shadow-lg' : 'border-gray-300'}`}>
                <div 
                    onDrop={handleDrop}
                    onDragOver={(e) => { e.preventDefault(); setIsDragOver(true); }}
                    onDragLeave={() => setIsDragOver(false)}
                    className="flex justify-center px-6 pt-8 pb-8 border-2 border-gray-300 border-dashed rounded-md"
                >
                    <div className="space-y-1 text-center">
                        <Icon.UserCircle className="mx-auto h-12 w-12 text-gray-400" />
                        <div className="flex text-sm text-gray-600">
                            <label htmlFor="cv-file-upload" className="relative cursor-pointer bg-white rounded-md font-medium text-[--org-color-main] hover:text-[--org-color-dark] focus-within:outline-none">
                                <span>Téléversez un ou plusieurs CV</span>
                                <input id="cv-file-upload" name="cv-file-upload" type="file" multiple className="sr-only" onChange={(e) => handleFileSelect(e.target.files)} />
                            </label>
                            <p className="pl-1">ou glissez-déposez</p>
                        </div>
                        <p className="text-xs text-gray-500">PDF, DOCX, PNG, JPG. L'IA analysera le contenu pour vous.</p>
                    </div>
                </div>
            </Card>

            <div>
                <h2 className="text-xl font-semibold text-gray-700 mb-4">Historique des Audits</h2>
                <div className="bg-white shadow-sm rounded-lg overflow-hidden border border-gray-200/80">
                    <table className="min-w-full divide-y divide-gray-200">
                        <thead className="bg-gray-50">
                            <tr>
                                <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Candidat</th>
                                <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Fichier Source</th>
                                <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Date d'Analyse</th>
                                <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Statut</th>
                                <th scope="col" className="relative px-6 py-3"><span className="sr-only">Actions</span></th>
                            </tr>
                        </thead>
                        <tbody className="bg-white divide-y divide-gray-200">
                            {sortedAudits.length > 0 ? sortedAudits.map(audit => (
                                <tr key={audit.id}>
                                    <td className="px-6 py-4 whitespace-nowrap">
                                        <div className="text-sm font-medium text-gray-900">{audit.candidate_name}</div>
                                    </td>
                                    <td className="px-6 py-4 whitespace-nowrap">
                                        <div className="text-sm text-gray-500 truncate" title={audit.source_file_name}>{audit.source_file_name}</div>
                                    </td>
                                    <td className="px-6 py-4 whitespace-nowrap">
                                        <div className="text-sm text-gray-500">{new Date(audit.created_at).toLocaleString('fr-FR')}</div>
                                    </td>
                                    <td className="px-6 py-4 whitespace-nowrap">
                                        {getStatusBadge(audit.status)}
                                    </td>
                                    <td className="px-6 py-4 whitespace-nowrap text-right text-sm font-medium space-x-2">
                                        <button 
                                            onClick={() => onSelectAudit(audit)} 
                                            disabled={audit.status === 'En cours' || audit.status === 'Échec'}
                                            className="text-[--org-color-main] hover:text-[--org-color-dark] disabled:text-gray-400 disabled:cursor-not-allowed font-semibold"
                                        >
                                            Voir le rapport
                                        </button>
                                        <button
                                            onClick={() => onDeleteAudit(audit)}
                                            className="text-red-600 hover:text-red-800"
                                            title="Supprimer l'audit"
                                        >
                                            <Icon.Trash className="w-4 h-4" />
                                        </button>
                                    </td>
                                </tr>
                            )) : (
                                <tr>
                                    <td colSpan={5} className="text-center py-10">
                                        <Icon.DocumentText className="h-12 w-12 mx-auto text-gray-300" />
                                        <h3 className="mt-2 text-sm font-medium text-gray-900">Aucun audit de candidat</h3>
                                        <p className="mt-1 text-sm text-gray-500">Commencez par téléverser un CV.</p>
                                    </td>
                                </tr>
                            )}
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    );
};
