import type { Database } from './services/supabaseClient';
type AbsenceRow = Database['public']['Tables']['absences']['Row'];

export const documentCategories = [
  { value: "Contrat", label: "Contrat" },
  { value: "reglement_interne", label: "Règlement Interne" },
  { value: "Diplome", label: "Diplome" },
  { value: "certificat_de_travail", label: "Certificat de travail" },
] as const;

export type DocumentCategory = (typeof documentCategories)[number]["value"];

export const getDocumentCategoryLabel = (value: DocumentCategory) => {
  return documentCategories.find(c => c.value === value)?.label || value;
}

export interface Employee {
  id: string;
  name: string;
  position: string;
  team: string;
  organizationId: string;
  contractStartDate: string | null;
  contractEndDate: string | null;
}

export interface ContentComplianceAnalysis {
  isCompliant: boolean;
  summary: string;
  findings: string[];
  issues: string[];
}

export interface HRDocument {
  id: string;
  name:string;
  category: DocumentCategory;
  uploadDate: string;
  url: string;
  storagePath: string;
  organizationId: string;
  employeeId?: string | null;
  contentComplianceAnalysis?: ContentComplianceAnalysis;
}

export interface PayrollAnomaly {
  employeeName: string;
  description: string;
  riskLevel: 'Faible' | 'Moyen' | 'Élevé';
}

export interface TopOvertimeEmployee {
  employeeName: string;
  overtimeHours: number;
  overtimeCost: number;
}

export interface PayrollAnalysis {
  totalOvertimeCost: number;
  totalOvertimeHours: number;
  potentialAnomalies: PayrollAnomaly[];
  topOvertimeEmployees: TopOvertimeEmployee[];
  summary: string;
}

export interface PayrollReport {
  id: string;
  created_at: string;
  organization_id: string;
  user_id: string;
  report_period: string;
  source_file_name: string;
  analysis_results: PayrollAnalysis;
}

export interface Organization {
    id: string;
    name: string;
    created_at: string;
    user_id: string;
}

export interface Absence {
  id: string;
  employeeId: string;
  startDate: string;
  endDate: string;
  reason: string;
  justified: boolean;
  organizationId: string;
  attachmentUrl: string | null;
  doctorName?: string | null;
  stampDetected?: boolean;
}

export interface Anomaly {
  employeeName: string;
  description: string;
  severity: 'Faible' | 'Moyen' | 'Élevé';
}

export interface ReportSummary {
  total: number;
  justified: number;
  unjustified: number;
  anomaliesCount: number;
}

export interface MonthlyAbsenceReportData {
  summary: ReportSummary;
  anomalies: Anomaly[];
  absences: AbsenceRow[];
}

export interface MonthlyAbsenceReport {
  id: string;
  created_at: string;
  organization_id: string;
  user_id: string;
  report_period: string;
  month: number;
  year: number;
  report_data: MonthlyAbsenceReportData;
}

export interface CandidateAnalysisReport {
  status: 'Conforme' | 'Non Conforme';
  summary: string;
  extractedData: {
    name: string | null;
    email: string | null;
    phone: string | null;
    education: {
      degree: string;
      institution: string;
      dates: string;
    }[];
    experience: {
      title: string;
      company: string;
      dates: string;
      description: string;
    }[];
    skills: string[];
    languages: string[];
  };
  findings: {
    type: 'inconsistency' | 'missing_info' | 'compliance_issue' | 'positive_point';
    description: string;
  }[];
}

export interface CandidateAudit {
  id: string;
  created_at: string;
  organization_id: string;
  user_id: string;
  candidate_name: string;
  source_file_name: string;
  storage_path: string;
  url: string;
  status: 'Conforme' | 'Non Conforme' | 'En cours' | 'Échec';
  analysis_report: CandidateAnalysisReport | null;
}


export enum View {
  Dashboard,
  Employees,
  Documents,
  CandidateAudit,
  EmployeeProfile,
  Payroll,
  Reports,
}
