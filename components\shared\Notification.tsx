import React from 'react';
import { Icon } from './Icon';
import { useNotification } from '../../hooks/useNotification';

const Notification: React.FC = () => {
    const { notification } = useNotification();

    if (!notification) {
        return null;
    }

    const { message, type } = notification;

    const baseClasses = "fixed top-5 left-1/2 -translate-x-1/2 z-[100] flex items-center p-4 mb-4 w-full max-w-lg rounded-lg shadow-lg text-white transition-all duration-300 ease-in-out transform animate-fade-in-down";

    const typeClasses = {
        success: 'bg-green-500',
        error: 'bg-red-600',
        info: 'bg-blue-500',
    };

    const icon = {
        success: <Icon.CheckCircle className="h-6 w-6"/>,
        error: <Icon.Warning className="h-6 w-6"/>,
        info: <Icon.Bell className="h-6 w-6"/>,
    };
    
    return (
        <div className={`${baseClasses} ${typeClasses[type]}`} role="alert">
            <div className="flex-shrink-0 mr-3">
                {icon[type]}
            </div>
            <div className="text-sm font-medium">
                {message}
            </div>
        </div>
    );
};

export default Notification;
