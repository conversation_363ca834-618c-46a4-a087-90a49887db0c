import React, { useState } from 'react';
import { HRDocument, Employee, DocumentCategory, documentCategories } from '../types';
import { Icon } from './shared/Icon';
import { Spinner } from './shared/Spinner';
import { useNotification } from '../hooks/useNotification';
import { getErrorMessage } from '../services/errorService';

interface AddOrEditDocumentModalProps {
  onClose: () => void;
  employees: Employee[];
  addDocument: (docData: Omit<HRDocument, 'id' | 'uploadDate' | 'url' | 'storagePath' | 'organizationId'> & { file: File }) => Promise<void>;
  updateDocument: (docData: Omit<HRDocument, 'uploadDate' | 'url'> & { file?: File }) => Promise<void>;
  documentToEdit?: HRDocument;
}

export const AddOrEditDocumentModal: React.FC<AddOrEditDocumentModalProps> = ({ onClose, employees, addDocument, updateDocument, documentToEdit }) => {
  const isEditMode = !!documentToEdit;
  const { showNotification } = useNotification();
  
  const [name, setName] = useState(documentToEdit?.name || '');
  const [category, setCategory] = useState<DocumentCategory>(documentToEdit?.category || 'Contrat');
  const [file, setFile] = useState<File | null>(null);
  const [employeeId, setEmployeeId] = useState<string | null>(documentToEdit?.employeeId || null);
  const [isSubmitting, setIsSubmitting] = useState(false);

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    if (!name || !category) {
      showNotification("Veuillez remplir tous les champs.", 'error');
      return;
    }
    if (!isEditMode && !file) {
      showNotification("Veuillez sélectionner un fichier pour le nouveau document.", 'error');
      return;
    }
    
    setIsSubmitting(true);
    
    try {
      const dataPayload = {
        name,
        category,
        employeeId,
      };

      if (isEditMode && documentToEdit) {
        await updateDocument({ ...documentToEdit, ...dataPayload, file: file || undefined });
      } else if (file) {
        await addDocument({ ...dataPayload, file });
      }
      onClose();
    } catch(error) {
        showNotification(`Erreur lors de la sauvegarde: ${getErrorMessage(error)}`, 'error');
    } finally {
        setIsSubmitting(false);
    }
  };

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex justify-center items-center z-50">
      <div className="bg-white rounded-xl shadow-2xl w-full max-w-md p-8 m-4 max-h-[90vh] overflow-y-auto">
        <div className="flex justify-between items-center mb-6">
          <h2 className="text-2xl font-bold text-gray-800 uppercase tracking-wide">
            {isEditMode ? 'Modifier le document' : 'Ajouter un document'}
          </h2>
          <button onClick={onClose} className="text-gray-400 hover:text-gray-600">
            <Icon.Close className="h-6 w-6" />
          </button>
        </div>
        <form onSubmit={handleSubmit}>
          <div className="space-y-4">
            <div>
              <label htmlFor="doc-name" className="block text-sm font-medium text-gray-700 mb-1">Nom du document</label>
              <input 
                type="text" 
                id="doc-name" 
                value={name} 
                onChange={e => setName(e.target.value)} 
                required 
                className="w-full p-2 border border-gray-300 rounded-lg focus:ring-[--org-color-main] focus:border-[--org-color-main]" 
                placeholder="Ex: Charte de bonne conduite" 
              />
            </div>
            <div>
              <label htmlFor="doc-category" className="block text-sm font-medium text-gray-700 mb-1">Catégorie</label>
              <select 
                id="doc-category" 
                value={category} 
                onChange={e => setCategory(e.target.value as DocumentCategory)} 
                required 
                className="w-full p-2 border border-gray-300 rounded-lg focus:ring-[--org-color-main] focus:border-[--org-color-main]"
              >
                {documentCategories.map(cat => (
                  <option key={cat.value} value={cat.value}>{cat.label}</option>
                ))}
              </select>
            </div>
             <div>
              <label htmlFor="doc-employee" className="block text-sm font-medium text-gray-700 mb-1">Assigner à un employé (Optionnel)</label>
              <select 
                id="doc-employee" 
                value={employeeId || ''} 
                onChange={e => setEmployeeId(e.target.value || null)}
                className="w-full p-2 border border-gray-300 rounded-lg focus:ring-[--org-color-main] focus:border-[--org-color-main]"
              >
                <option value="">Général (toute l'organisation)</option>
                {employees.map(emp => (
                  <option key={emp.id} value={emp.id}>{emp.name}</option>
                ))}
              </select>
            </div>
            <div>
                <label htmlFor="doc-file" className="block text-sm font-medium text-gray-700 mb-1">
                    Fichier {isEditMode ? '(Optionnel: pour remplacer)' : '(Requis)'}
                </label>
                <div className="mt-1 flex justify-center px-6 pt-5 pb-6 border-2 border-gray-300 border-dashed rounded-md">
                    <div className="space-y-1 text-center">
                        <Icon.Upload className="mx-auto h-12 w-12 text-gray-400" />
                        <div className="flex text-sm text-gray-600">
                            <label htmlFor="doc-file-input" className="relative cursor-pointer bg-white rounded-md font-medium text-[--org-color-main] hover:text-[--org-color-dark] focus-within:outline-none focus-within:ring-2 focus-within:ring-offset-2 focus-within:ring-[--org-color-main]">
                                <span>Téléversez un fichier</span>
                                <input id="doc-file-input" name="doc-file-input" type="file" className="sr-only" onChange={(e) => setFile(e.target.files?.[0] || null)} />
                            </label>
                            <p className="pl-1">ou glissez-déposez</p>
                        </div>
                        {file ? (
                            <p className="text-sm text-green-600 font-semibold pt-2">{file.name}</p>
                        ) : isEditMode ? (
                           <p className="text-xs text-gray-500">Fichier existant conservé</p>
                        ) : (
                            <p className="text-xs text-gray-500">PDF, DOCX, PNG, JPG, etc.</p>
                        )}
                    </div>
                </div>
            </div>
          </div>
          <div className="mt-8 flex justify-end space-x-4">
            <button type="button" onClick={onClose} className="py-2 px-4 bg-gray-200 text-gray-800 font-semibold rounded-lg hover:bg-gray-300">Annuler</button>
            <button type="submit" disabled={isSubmitting} className="py-2 px-6 bg-[--org-color-main] text-white font-semibold rounded-lg shadow-md hover:bg-[--org-color-dark] disabled:bg-opacity-60 disabled:cursor-not-allowed flex items-center">
              {isSubmitting ? <><Spinner size="sm" color="white"/> <span className="ml-2">Sauvegarde...</span></> : (isEditMode ? 'Sauvegarder' : 'Ajouter')}
            </button>
          </div>
        </form>
      </div>
    </div>
  );
};