import React from 'react';
import { CandidateAudit } from '../types';
import { Icon } from './shared/Icon';
import { generateAuditReportPDF, generateStandardizedCVPDF } from '../services/reportService';
import { Card } from './shared/Card';

interface AuditReportModalProps {
  audit: CandidateAudit;
  onClose: () => void;
}

const FindingItem: React.FC<{ finding: CandidateAudit['analysis_report']['findings'][0] }> = ({ finding }) => {
    const styles = {
        inconsistency: { icon: Icon.Warning, color: 'text-red-500', bg: 'bg-red-50' },
        missing_info: { icon: Icon.QuestionMarkCircle, color: 'text-yellow-500', bg: 'bg-yellow-50' },
        compliance_issue: { icon: Icon.ShieldCheck, color: 'text-red-600', bg: 'bg-red-50' },
        positive_point: { icon: Icon.CheckCircle, color: 'text-green-500', bg: 'bg-green-50' },
    };
    const style = styles[finding.type] || styles.missing_info;
    const IconComponent = style.icon;

    return (
        <li className={`flex items-start p-3 rounded-lg ${style.bg}`}>
            <IconComponent className={`h-5 w-5 mr-3 flex-shrink-0 mt-0.5 ${style.color}`} />
            <p className="text-sm text-gray-700">{finding.description}</p>
        </li>
    );
};

export const AuditReportModal: React.FC<AuditReportModalProps> = ({ audit, onClose }) => {
  if (!audit.analysis_report) return null;

  const { analysis_report: report } = audit;
  const { extractedData } = report;

  return (
    <div className="fixed inset-0 bg-black bg-opacity-60 flex justify-center items-center z-50 p-4">
      <div className="bg-white rounded-xl shadow-2xl w-full max-w-6xl max-h-[90vh] flex flex-col">
        {/* Header */}
        <div className="flex justify-between items-center p-5 border-b border-gray-200">
          <div>
            <h2 className="text-xl font-bold text-gray-800">Rapport d'Analyse de Candidature</h2>
            <p className="text-sm text-gray-500 truncate" title={extractedData.name || ''}>
              {extractedData.name || 'Candidat Anonyme'}
            </p>
          </div>
          <button onClick={onClose} className="text-gray-400 hover:text-gray-600"><Icon.Close className="h-6 w-6" /></button>
        </div>

        {/* Body */}
        <div className="flex-grow overflow-y-auto p-6 grid grid-cols-1 lg:grid-cols-2 gap-6">
          {/* Left: CV Preview */}
          <div className="bg-gray-100 rounded-lg flex items-center justify-center p-2 border border-gray-200 min-h-[400px]">
            <iframe src={audit.url} title={`CV de ${extractedData.name}`} className="w-full h-full min-h-[60vh] border-0 rounded-md" />
          </div>

          {/* Right: AI Report */}
          <div className="space-y-6">
            <h3 className="text-lg font-semibold text-[--org-color-main] flex items-center">
              <Icon.Sparkles className="h-5 w-5 mr-2" />
              Rapport de l'IA
            </h3>

            {/* Status & Summary */}
            <Card className="p-4">
                <div className={`p-3 mb-4 rounded-lg flex items-center text-sm font-semibold ${report.status === 'Conforme' ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800'}`}>
                    {report.status === 'Conforme' ? <Icon.CheckCircle className="h-5 w-5 mr-2" /> : <Icon.Warning className="h-5 w-5 mr-2" />}
                    Statut global : {report.status}
                </div>
                <div>
                    <h4 className="font-semibold text-gray-700 mb-1">Synthèse</h4>
                    <p className="text-sm text-gray-600">{report.summary}</p>
                </div>
            </Card>
            
             {/* Findings */}
            {report.findings.length > 0 && (
                 <Card className="p-4">
                    <h4 className="font-semibold text-gray-700 mb-3">Points d'Attention</h4>
                    <ul className="space-y-2">
                        {report.findings.map((finding, index) => (
                            <FindingItem key={index} finding={finding} />
                        ))}
                    </ul>
                </Card>
            )}

            {/* Extracted Data */}
            <Card className="p-4">
                <h4 className="font-semibold text-gray-700 mb-3">Informations Extraites</h4>
                <div className="space-y-4 text-sm">
                    {extractedData.experience.length > 0 && (
                        <div>
                            <h5 className="font-semibold text-gray-600 mb-1">Expérience</h5>
                            <ul className="list-disc list-inside space-y-1 pl-2">
                                {extractedData.experience.map((exp, i) => <li key={i}>{exp.title} chez {exp.company} ({exp.dates})</li>)}
                            </ul>
                        </div>
                    )}
                    {extractedData.education.length > 0 && (
                        <div>
                            <h5 className="font-semibold text-gray-600 mb-1">Formation</h5>
                            <ul className="list-disc list-inside space-y-1 pl-2">
                                {extractedData.education.map((edu, i) => <li key={i}>{edu.degree}, {edu.institution} ({edu.dates})</li>)}
                            </ul>
                        </div>
                    )}
                     {extractedData.skills.length > 0 && (
                        <div>
                            <h5 className="font-semibold text-gray-600 mb-1">Compétences</h5>
                            <p className="text-gray-800">{extractedData.skills.join(', ')}</p>
                        </div>
                    )}
                </div>
            </Card>
          </div>
        </div>
        {/* Footer */}
        <div className="p-4 bg-gray-50 border-t border-gray-200 flex flex-wrap justify-end gap-3">
            <button type="button" onClick={onClose} className="py-2 px-4 bg-gray-200 text-gray-800 font-semibold rounded-lg hover:bg-gray-300">
                Fermer
            </button>
            <button 
                type="button"
                onClick={() => generateStandardizedCVPDF(report)}
                className="py-2 px-4 bg-white text-[--org-color-main] border border-[--org-color-main] font-semibold rounded-lg hover:bg-gray-50 flex items-center"
            >
                <Icon.Download className="h-4 w-4 mr-2" />
                CV Standardisé (PDF)
            </button>
            <button 
                type="button" 
                onClick={() => generateAuditReportPDF(report)}
                className="py-2 px-4 bg-[--org-color-main] text-white font-semibold rounded-lg hover:bg-[--org-color-dark] flex items-center"
            >
                <Icon.DocumentText className="h-4 w-4 mr-2" />
                Rapport d'Audit (PDF)
            </button>
        </div>
      </div>
    </div>
  );
};
