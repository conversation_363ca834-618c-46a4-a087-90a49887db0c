import React from 'react';
import { Icon } from './Icon';
import { useNotification } from '../../hooks/useNotification';

interface ServerErrorModalProps {
  isOpen: boolean;
  onClose: () => void;
  title: string;
  children: React.ReactNode; // For the descriptive message
  sqlScript: string; // The actual SQL code to be copied and displayed
}

export const ServerErrorModal: React.FC<ServerErrorModalProps> = ({
  isOpen,
  onClose,
  title,
  children,
  sqlScript
}) => {
  if (!isOpen) return null;

  const { showNotification } = useNotification();

  const handleCopy = () => {
    navigator.clipboard.writeText(sqlScript)
        .then(() => showNotification('Script SQL copié dans le presse-papiers !', 'success'))
        .catch(err => showNotification('Échec de la copie du script.', 'error'));
  };

  return (
    <div className="fixed inset-0 bg-black bg-opacity-60 flex justify-center items-center z-[999] p-4" aria-modal="true" role="dialog">
      <div className="bg-white rounded-xl shadow-2xl w-full max-w-2xl p-6 m-4 modal-content-animate flex flex-col max-h-[90vh]">
        <div className="flex justify-between items-center mb-4">
            <div className="flex items-center">
                <div className="mx-auto flex-shrink-0 flex items-center justify-center h-12 w-12 rounded-full bg-red-100 mr-4">
                    <Icon.Warning className="h-6 w-6 text-red-600" />
                </div>
                <h3 className="text-lg leading-6 font-bold text-gray-900" id="modal-title">
                    {title}
                </h3>
            </div>
             <button onClick={onClose} className="text-gray-400 hover:text-gray-600">
                <Icon.Close className="h-6 w-6" />
            </button>
        </div>
        <div className="flex-grow overflow-y-auto pr-2">
            <div className="mt-2 text-sm text-gray-600 space-y-4">
                {children}
            </div>
             <div className="mt-4">
                <pre className="bg-gray-800 text-white p-4 rounded-md font-mono text-sm overflow-x-auto whitespace-pre-wrap">{sqlScript}</pre>
            </div>
        </div>
        <div className="mt-6 pt-4 border-t flex flex-col sm:flex-row justify-between items-center gap-3">
            <p className="text-xs text-gray-500 text-center sm:text-left">Exécutez ce script dans l'Éditeur SQL de votre projet Supabase.</p>
            <div className="flex gap-3">
                 <button
                    type="button"
                    onClick={handleCopy}
                    className="w-full sm:w-auto inline-flex items-center justify-center rounded-lg border border-gray-300 shadow-sm px-4 py-2 bg-white text-base font-medium text-gray-700 hover:bg-gray-50 sm:text-sm"
                  >
                    <Icon.Document className="h-4 w-4 mr-2" /> Copier le script
                  </button>
                <button
                    type="button"
                    onClick={onClose}
                    className="w-full sm:w-auto inline-flex justify-center rounded-lg border border-transparent shadow-sm px-4 py-2 bg-[--org-color-main] text-base font-medium text-white hover:bg-[--org-color-dark] sm:text-sm"
                >
                    Fermer
                </button>
            </div>
        </div>
      </div>
    </div>
  );
};