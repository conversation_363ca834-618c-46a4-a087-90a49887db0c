/**
 * Generates a consistent HSL color trio from a string.
 * Ensures good contrast and visual appeal.
 * @param str The input string (e.g., an organization ID).
 * @returns An object with 'main', 'dark', and 'light' HSL color strings.
 */
export const generateColorFromString = (str: string): { main: string; dark: string; light: string } => {
    if (!str) {
        // Default LICA colors
        return { main: '#0A2940', dark: '#072136', light: '#F0F5F9' };
    }

    let hash = 0;
    for (let i = 0; i < str.length; i++) {
        hash = str.charCodeAt(i) + ((hash << 5) - hash);
        hash = hash & hash; // Ensure 32-bit integer
    }

    const hue = Math.abs(hash % 360);
    
    // Using HSL for easy manipulation of lightness and saturation
    const main = `hsl(${hue}, 60%, 45%)`; // A balanced, strong color
    const dark = `hsl(${hue}, 60%, 35%)`; // A darker shade for hovers
    const light = `hsl(${hue}, 75%, 96%)`; // A very light, tinted background color

    return { main, dark, light };
};
