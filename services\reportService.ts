
import { Absence, Anomaly, Employee, ReportSummary, CandidateAnalysisReport } from '../types';
import { analyzeAbsencesForAnomalies } from './geminiService';
import { getErrorMessage } from './errorService';
import jsPDF from 'jspdf';
import 'jspdf-autotable';


// Extend jsPDF with autoTable for TypeScript
interface jsPDFWithAutoTable extends jsPDF {
  autoTable: (options: any) => jsPDF;
}


export const generateAbsenceReport = async (
  absences: Absence[],
  employees: Employee[],
  onProgress?: (message: string) => void
): Promise<{ summary: ReportSummary; anomalies: Anomaly[] }> => {

  const justified = absences.filter(a => a.justified).length;
  const unjustified = absences.length - justified;

  let anomalies: Anomaly[] = [];

  if (absences.length > 0) {
    try {
      onProgress?.("Analyse des anomalies d'absence en cours...");
      // Don't block the whole report if AI fails. Return an empty array of anomalies.
      anomalies = await analyzeAbsencesForAnomalies(absences, employees);
      onProgress?.("Analyse des anomalies terminée.");
    } catch (error) {
      console.error("Anomaly detection failed, continuing without it.", getErrorMessage(error));
      onProgress?.("Analyse des anomalies échouée, rapport généré sans analyse IA.");
    }
  }

  const summary: ReportSummary = {
    total: absences.length,
    justified,
    unjustified,
    anomaliesCount: anomalies.length
  };

  return { summary, anomalies };
};


export const generateStandardizedCVPDF = (report: CandidateAnalysisReport) => {
    const doc = new jsPDF();
    const data = report.extractedData;

    // Header
    doc.setFontSize(22);
    doc.setFont('helvetica', 'bold');
    doc.setTextColor(10, 41, 64); // --lica-blue
    doc.text(data.name || 'Candidat Anonyme', 20, 20);
    
    doc.setFontSize(10);
    doc.setFont('helvetica', 'normal');
    doc.setTextColor(100, 116, 139); // gray-500
    if (data.email) doc.text(data.email, 20, 28);
    if (data.phone) doc.text(data.phone, (data.email ? 60 : 20), 28);
    
    let y = 45;

    // Experience
    if(data.experience.length > 0) {
        doc.setFontSize(14);
        doc.setFont('helvetica', 'bold');
        doc.setTextColor(10, 41, 64);
        doc.text('Expériences Professionnelles', 20, y);
        doc.setLineWidth(0.5);
        doc.line(20, y + 2, 190, y + 2);
        y += 10;
        doc.setFontSize(10);
        
        data.experience.forEach(exp => {
            doc.setFont('helvetica', 'bold');
            doc.setTextColor(51, 65, 85); // gray-700
            doc.text(`${exp.title}`, 20, y);
            doc.setFont('helvetica', 'normal');
            doc.text(` | ${exp.company}`, doc.getTextWidth(exp.title) + 21, y);

            doc.setFont('helvetica', 'italic');
            doc.setTextColor(100, 116, 139);
            doc.text(exp.dates, 190, y, { align: 'right' });
            y += 6;
            
            doc.setFont('helvetica', 'normal');
            doc.setTextColor(51, 65, 85);
            const descLines = doc.splitTextToSize(exp.description, 165);
            doc.text(descLines, 25, y);
            y += descLines.length * 5 + 5;
        });
    }

    // Education
    if (data.education.length > 0) {
        y += 5;
        doc.setFontSize(14);
        doc.setFont('helvetica', 'bold');
        doc.setTextColor(10, 41, 64);
        doc.text('Formation', 20, y);
        doc.setLineWidth(0.5);
        doc.line(20, y + 2, 190, y + 2);
        y += 2;
        (doc as any).autoTable({
            startY: y,
            head: [['Diplôme', 'Établissement', 'Période']],
            body: data.education.map(edu => [edu.degree, edu.institution, edu.dates]),
            theme: 'striped',
            headStyles: { fillColor: [10, 41, 64] },
            margin: { left: 20, right: 20 },
        });
        y = (doc as any).autoTable.previous.finalY + 10;
    }

    // Skills & Languages
    const renderSection = (title: string, items: string[]) => {
        if(items.length === 0) return;
        doc.setFontSize(14);
        doc.setFont('helvetica', 'bold');
        doc.setTextColor(10, 41, 64);
        doc.text(title, 20, y);
        doc.setLineWidth(0.5);
        doc.line(20, y + 2, 190, y + 2);
        y += 10;
        doc.setFontSize(10);
        doc.setTextColor(51, 65, 85);
        doc.setFont('helvetica', 'normal');
        
        const bubblePadding = 3;
        const bubbleMargin = 2;
        let currentX = 20;

        items.forEach(item => {
            const textWidth = doc.getTextWidth(item);
            const bubbleWidth = textWidth + bubblePadding * 2;
            if (currentX + bubbleWidth > 190) {
                y += 10;
                currentX = 20;
            }
            doc.setFillColor(240, 245, 249); // --lica-blue-light
            doc.roundedRect(currentX, y - 5, bubbleWidth, 7, 3, 3, 'F');
            doc.text(item, currentX + bubblePadding, y);
            currentX += bubbleWidth + bubbleMargin;
        });
        y += 20;
    };
    
    renderSection('Compétences', data.skills);
    renderSection('Langues', data.languages);
    
    doc.save(`CV_Standardise_${data.name?.replace(' ', '_') || 'candidat'}.pdf`);
};

export const generateAuditReportPDF = (report: CandidateAnalysisReport) => {
    const doc = new jsPDF();
    const data = report.extractedData;

    // Header
    doc.setFontSize(18);
    doc.setTextColor(10, 41, 64); // --lica-blue
    doc.text(`Rapport d'Audit de Candidature`, 105, 20, { align: 'center' });
    doc.setFontSize(14);
    doc.setTextColor(0, 0, 0);
    doc.text(data.name || 'Candidat Anonyme', 105, 28, { align: 'center' });

    let y = 40;

    // Summary
    doc.setFontSize(12);
    doc.setFont('helvetica', 'bold');
    doc.text('Statut Global:', 20, y);
    doc.setFont('helvetica', 'normal');
    if (report.status === 'Conforme') {
        doc.setTextColor(34, 197, 94);
    } else {
        doc.setTextColor(239, 68, 68);
    }
    doc.text(report.status, 55, y);
    doc.setTextColor(0,0,0);
    y += 10;
    doc.setFont('helvetica', 'bold');
    doc.text('Résumé de l\'IA:', 20, y);
    y += 7;
    doc.setFont('helvetica', 'normal');
    const summaryLines = doc.splitTextToSize(report.summary, 170);
    doc.setFillColor(248, 250, 252); // gray-50
    doc.rect(20, y - 5, 170, summaryLines.length * 5 + 8, 'F');
    doc.text(summaryLines, 22, y);
    y += summaryLines.length * 5 + 10;

    // Findings
    if (report.findings.length > 0) {
        doc.setFontSize(14);
        doc.setFont('helvetica', 'bold');
        doc.text('Points d\'Attention Détectés', 20, y);
        y += 2;
        (doc as any).autoTable({
            startY: y,
            head: [['Type', 'Description']],
            body: report.findings.map(finding => {
                let typeLabel = '';
                switch(finding.type) {
                    case 'inconsistency': typeLabel = 'Incohérence'; break;
                    case 'missing_info': typeLabel = 'Info manquante'; break;
                    case 'compliance_issue': typeLabel = 'Non-conformité'; break;
                    case 'positive_point': typeLabel = 'Point Positif'; break;
                }
                return [typeLabel, finding.description];
            }),
            theme: 'grid',
            headStyles: { fillColor: [10, 41, 64] },
            didParseCell: (data: any) => {
                const finding = report.findings[data.row.index];
                if (data.column.index === 0 && finding) {
                     if (finding.type === 'compliance_issue' || finding.type === 'inconsistency') {
                        data.cell.styles.textColor = [220, 38, 38]; // red-600
                        data.cell.styles.fontStyle = 'bold';
                    }
                    if (finding.type === 'missing_info') {
                        data.cell.styles.textColor = [234, 179, 8]; // yellow-500
                    }
                    if (finding.type === 'positive_point') {
                         data.cell.styles.textColor = [22, 163, 74]; // green-600
                    }
                }
            }
        });
    } else {
        doc.setFontSize(12);
        doc.setFont('helvetica', 'bold');
        doc.text('Aucun Point d\'Attention Particulier Détecté', 20, y);
    }
    
    doc.save(`Rapport_Audit_${data.name?.replace(' ', '_') || 'candidat'}.pdf`);
};
