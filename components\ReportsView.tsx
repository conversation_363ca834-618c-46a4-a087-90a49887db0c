import React, { useState } from 'react';
import type { Database } from '../services/supabaseClient';
import { Anomaly, Employee, MonthlyAbsenceReport, ReportSummary } from '../types';
import { Card } from './shared/Card';
import { Icon } from './shared/Icon';
import { DoughnutChart } from './shared/DoughnutChart';
import { Spinner } from './shared/Spinner';
import { MonthlyArchiveModal } from './MonthlyArchiveModal';

type AbsenceRow = Database['public']['Tables']['absences']['Row'];

interface ReportsViewProps {
  summary: ReportSummary | null;
  anomalies: Anomaly[];
  absences: AbsenceRow[];
  employees: Employee[];
  archivedReports: MonthlyAbsenceReport[];
  onArchive: (month: number, year: number, periodLabel: string) => Promise<void>;
  onDeleteArchive: (reportId: string) => void;
  isArchiveFeatureAvailable: boolean;
}

const AnomalyItem: React.FC<{ anomaly: Anomaly }> = ({ anomaly }) => {
    const riskStyles = {
        'Élevé': { bg: 'bg-red-100', text: 'text-red-800', border: 'border-red-200' },
        'Moyen': { bg: 'bg-yellow-100', text: 'text-yellow-800', border: 'border-yellow-200' },
        'Faible': { bg: 'bg-blue-100', text: 'text-blue-800', border: 'border-blue-200' },
    };
    const style = riskStyles[anomaly.severity] || riskStyles['Faible'];

    return (
        <li className={`p-4 rounded-lg border ${style.bg}`}>
            <div className="flex justify-between items-start">
                 <p className="font-semibold text-gray-800">{anomaly.employeeName}</p>
                <span className={`px-2 inline-flex text-xs leading-5 font-semibold rounded-full ${style.bg} ${style.text}`}>{anomaly.severity}</span>
            </div>
            <p className={`text-sm ${style.text} mt-1`}>{anomaly.description}</p>
        </li>
    );
};

const FullHistoryTable: React.FC<{ absences: AbsenceRow[]; employees: Employee[] }> = ({ absences, employees }) => {
    const employeeMap = new Map(employees.map(e => [e.id, e.name]));
    if(absences.length === 0) return <p className="text-center text-gray-500 py-8">Aucune absence pour cette période.</p>;
    return (
      <div className="overflow-x-auto">
        <table className="min-w-full divide-y divide-gray-200">
          <thead className="bg-gray-50">
            <tr>
              <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Employé</th>
              <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Période</th>
              <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Motif</th>
              <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Statut</th>
            </tr>
          </thead>
          <tbody className="bg-white divide-y divide-gray-200">
            {absences.map((absence) => (
              <tr key={absence.id}>
                <td className="px-6 py-4 whitespace-nowrap"><div className="text-sm font-medium text-gray-900">{employeeMap.get(absence.employee_id) || 'Inconnu'}</div></td>
                <td className="px-6 py-4 whitespace-nowrap"><div className="text-sm text-gray-900">{new Date(absence.start_date).toLocaleDateString('fr-FR')} - {new Date(absence.end_date).toLocaleDateString('fr-FR')}</div></td>
                <td className="px-6 py-4"><div className="text-sm text-gray-900">{absence.reason}</div></td>
                <td className="px-6 py-4 whitespace-nowrap">
                  {absence.justified ? (
                    <span className="px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-green-100 text-green-800">Justifiée</span>
                  ) : (
                    <span className="px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-red-100 text-red-800">Non justifiée</span>
                  )}
                </td>
              </tr>
            ))}
          </tbody>
        </table>
      </div>
    );
};

const ReportDetailView: React.FC<{summary: ReportSummary, anomalies: Anomaly[], absences: AbsenceRow[], employees: Employee[]}> = ({summary, anomalies, absences, employees}) => {
    const [activeTab, setActiveTab] = useState<'anomalies' | 'history'>('anomalies');
    const chartData = [
      { value: summary.justified, color: '#22C55E', label: 'Justifiées' },
      { value: summary.unjustified, color: '#EF4444', label: 'Non Justifiées' },
    ];

    return (
        <Card>
            <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
                <div className="lg:col-span-1 flex flex-col items-center justify-center">
                    <DoughnutChart data={chartData} />
                    <div className="flex justify-center space-x-4 mt-4">
                        <div className="flex items-center"><div className="h-3 w-3 rounded-full bg-green-500 mr-2"></div><span className="text-sm">Justifiées</span></div>
                        <div className="flex items-center"><div className="h-3 w-3 rounded-full bg-red-500 mr-2"></div><span className="text-sm">Non justifiées</span></div>
                    </div>
                </div>
                <div className="lg:col-span-2">
                    <div className="border-b border-gray-200">
                      <nav className="-mb-px flex space-x-4" aria-label="Tabs">
                        <button onClick={() => setActiveTab('anomalies')} className={`whitespace-nowrap py-3 px-1 border-b-2 font-medium text-sm ${activeTab === 'anomalies' ? 'border-[--org-color-main] text-[--org-color-main]' : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'}`}>
                          Anomalies Détectées par l'IA
                        </button>
                        <button onClick={() => setActiveTab('history')} className={`whitespace-nowrap py-3 px-1 border-b-2 font-medium text-sm ${activeTab === 'history' ? 'border-[--org-color-main] text-[--org-color-main]' : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'}`}>
                          Historique Complet
                        </button>
                      </nav>
                    </div>

                    <div className="mt-4 max-h-[400px] overflow-y-auto pr-2">
                        {activeTab === 'anomalies' && (
                            <ul className="space-y-3">
                                {anomalies.length > 0 ? (
                                    anomalies.map((anomaly, index) => <AnomalyItem key={index} anomaly={anomaly} />)
                                ) : (
                                    <p className="text-center text-gray-500 py-8">Aucune anomalie détectée par l'IA.</p>
                                )}
                            </ul>
                        )}
                         {activeTab === 'history' && <FullHistoryTable absences={absences} employees={employees} />}
                    </div>
                </div>
            </div>
        </Card>
    );
};


export const ReportsView: React.FC<ReportsViewProps> = ({ summary, anomalies, absences, employees, archivedReports, onArchive, onDeleteArchive, isArchiveFeatureAvailable }) => {
  const [activeTab, setActiveTab] = useState<'live' | 'archived'>('live');
  const [selectedArchivedReport, setSelectedArchivedReport] = useState<MonthlyAbsenceReport | null>(null);
  const [showArchiveModal, setShowArchiveModal] = useState(false);

  const LiveDashboard = () => {
      if (!summary) {
        return (
          <div className="flex flex-col items-center justify-center h-full text-center mt-10">
            {absences.length > 0 ? (
              <>
                <Spinner size="lg" />
                <p className="mt-4 text-lg text-gray-600 font-semibold">Analyse IA des absences en cours...</p>
                <p className="text-sm text-gray-500">Veuillez patienter.</p>
              </>
            ) : (
              <>
                <Icon.Report className="h-20 w-20 text-gray-300" />
                <h2 className="mt-4 text-xl font-semibold text-gray-700">Aucune donnée d'absence à analyser</h2>
                <p className="mt-1 text-gray-500">Commencez par enregistrer des absences depuis les profils des employés.</p>
              </>
            )}
          </div>
        );
      }
      return (
        <div className="space-y-8 mt-4">
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
                <Card><div className="flex items-center"><div className="p-3 bg-gray-100 rounded-full mr-4"><Icon.Calendar className="h-6 w-6 text-gray-600"/></div><div><p className="text-sm font-medium text-gray-500">Total Absences</p><p className="text-3xl font-bold text-gray-800">{summary.total}</p></div></div></Card>
                <Card><div className="flex items-center"><div className="p-3 bg-green-100 rounded-full mr-4"><Icon.CheckCircle className="h-6 w-6 text-green-600"/></div><div><p className="text-sm font-medium text-gray-500">Justifiées</p><p className="text-3xl font-bold text-gray-800">{summary.justified}</p></div></div></Card>
                <Card><div className="flex items-center"><div className="p-3 bg-red-100 rounded-full mr-4"><Icon.Close className="h-6 w-6 text-red-600"/></div><div><p className="text-sm font-medium text-gray-500">Non Justifiées</p><p className="text-3xl font-bold text-gray-800">{summary.unjustified}</p></div></div></Card>
                <Card><div className="flex items-center"><div className="p-3 bg-yellow-100 rounded-full mr-4"><Icon.Warning className="h-6 w-6 text-yellow-600"/></div><div><p className="text-sm font-medium text-gray-500">Anomalies Détectées</p><p className="text-3xl font-bold text-gray-800">{summary.anomaliesCount}</p></div></div></Card>
            </div>
            <ReportDetailView summary={summary} anomalies={anomalies} absences={absences} employees={employees} />
        </div>
      );
  }

  const ArchivedReportsDashboard = () => (
    <div className="grid grid-cols-1 lg:grid-cols-3 gap-6 mt-4">
        <div className="lg:col-span-1">
             <div className="flex justify-between items-center mb-2">
                <h2 className="text-xl font-semibold text-gray-700">Archives</h2>
                <button 
                    onClick={() => setShowArchiveModal(true)} 
                    disabled={!isArchiveFeatureAvailable}
                    title={!isArchiveFeatureAvailable ? "Fonctionnalité non disponible. La table 'monthly_absence_reports' doit être créée dans la base de données." : "Générer un rapport mensuel"}
                    className="flex items-center text-sm font-semibold bg-[--org-color-main] text-white py-1 px-3 rounded-lg hover:bg-[--org-color-dark] disabled:bg-gray-400 disabled:cursor-not-allowed transition-colors"
                >
                    <Icon.Plus className="h-4 w-4 mr-1"/>Générer
                </button>
             </div>
             <Card className="p-2 max-h-[70vh] overflow-y-auto">
                {!isArchiveFeatureAvailable && (
                    <div className="p-3 bg-yellow-50 text-yellow-800 rounded-lg text-xs flex items-start">
                        <Icon.Warning className="h-4 w-4 mr-2 flex-shrink-0 mt-0.5"/>
                        <span>La fonction d'archivage est désactivée car la table requise est manquante dans la base de données.</span>
                    </div>
                )}
                {archivedReports.length === 0 ? (
                    <p className="p-4 text-center text-gray-500">Aucun rapport archivé.</p>
                ) : (
                    <ul className="space-y-1">
                        {archivedReports.map(report => (
                            <li key={report.id}>
                                <button onClick={() => setSelectedArchivedReport(report)} className={`w-full text-left p-3 rounded-lg flex justify-between items-center ${selectedArchivedReport?.id === report.id ? 'bg-[--org-color-light]' : 'hover:bg-gray-100'}`}>
                                    <div>
                                        <p className="font-semibold text-gray-800">{report.report_period}</p>
                                        <p className="text-xs text-gray-500">{new Date(report.created_at).toLocaleDateString('fr-FR')}</p>
                                    </div>
                                    <Icon.ChevronRight className="h-5 w-5 text-gray-400" />
                                </button>
                            </li>
                        ))}
                    </ul>
                )}
             </Card>
        </div>
        <div className="lg:col-span-2 space-y-4">
            {!selectedArchivedReport ? (
                 <Card className="flex items-center justify-center h-96 text-center">
                    <div>
                        <Icon.ArchiveBox className="h-16 w-16 text-gray-300 mx-auto mb-4"/>
                        <h3 className="font-semibold text-gray-500">Sélectionnez un rapport</h3>
                        <p className="text-sm text-gray-400">Choisissez un rapport dans la liste pour voir les détails.</p>
                    </div>
                </Card>
            ) : (
                <>
                    <div className="flex justify-between items-center">
                        <h2 className="text-xl font-semibold text-gray-700">Rapport de {selectedArchivedReport.report_period}</h2>
                         <button
                            onClick={() => onDeleteArchive(selectedArchivedReport.id)}
                            className="flex items-center text-sm font-semibold text-red-600 bg-red-100 py-1 px-3 rounded-lg hover:bg-red-200"
                        >
                            <Icon.Trash className="h-4 w-4 mr-2" />
                            Supprimer
                        </button>
                    </div>
                    <ReportDetailView summary={selectedArchivedReport.report_data.summary} anomalies={selectedArchivedReport.report_data.anomalies} absences={selectedArchivedReport.report_data.absences} employees={employees} />
                </>
            )}
        </div>
     </div>
  );

  return (
    <div>
      <div className="flex justify-between items-center mb-4 pb-4 border-b border-gray-200">
        <h1 className="text-3xl font-bold text-[--org-color-main] uppercase tracking-wide">Rapports d'Absence</h1>
      </div>
      
       <div className="border-b border-gray-200">
            <nav className="-mb-px flex space-x-6" aria-label="Tabs">
            <button onClick={() => setActiveTab('live')} className={`shrink-0 flex items-center gap-2 whitespace-nowrap py-3 px-1 border-b-2 font-medium text-sm ${activeTab === 'live' ? 'border-[--org-color-main] text-[--org-color-main]' : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'}`}>
                <Icon.Dashboard className="h-5 w-5"/> Tableau de Bord Actuel
            </button>
            <button onClick={() => setActiveTab('archived')} className={`shrink-0 flex items-center gap-2 whitespace-nowrap py-3 px-1 border-b-2 font-medium text-sm ${activeTab === 'archived' ? 'border-[--org-color-main] text-[--org-color-main]' : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'}`}>
               <Icon.ArchiveBox className="h-5 w-5"/> Rapports Mensuels Archivés
            </button>
            </nav>
        </div>

      {activeTab === 'live' && <LiveDashboard />}
      {activeTab === 'archived' && <ArchivedReportsDashboard />}
      {showArchiveModal && <MonthlyArchiveModal onClose={() => setShowArchiveModal(false)} onArchive={onArchive} />}

    </div>
  );
};