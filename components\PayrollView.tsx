import React, { useState, useCallback, useRef, useEffect } from 'react';
import { Card } from './shared/Card';
import { Icon } from './shared/Icon';
import { Spinner } from './shared/Spinner';
import { useNotification } from '../hooks/useNotification';
import { analyzePayrollData } from '../services/geminiService';
import { PayrollAnalysis, PayrollAnomaly, PayrollReport } from '../types';
import { getErrorMessage } from '../services/errorService';

const TabButton: React.FC<{
  label: string;
  icon: React.ReactNode;
  isActive: boolean;
  onClick: () => void;
}> = ({ label, icon, isActive, onClick }) => (
  <button
    onClick={onClick}
    className={`flex items-center px-4 py-2 text-sm font-semibold rounded-md transition-colors duration-200 ${
      isActive
        ? 'bg-white text-[--org-color-main] shadow'
        : 'text-gray-600 hover:bg-white/60'
    }`}
  >
    {icon}
    <span className="ml-2">{label}</span>
  </button>
);


const AnomalyItem: React.FC<{ anomaly: PayrollAnomaly }> = ({ anomaly }) => {
    const riskStyles = {
        'Élevé': { bg: 'bg-red-100', text: 'text-red-800', border: 'border-red-200' },
        'Moyen': { bg: 'bg-yellow-100', text: 'text-yellow-800', border: 'border-yellow-200' },
        'Faible': { bg: 'bg-blue-100', text: 'text-blue-800', border: 'border-blue-200' },
    };
    const style = riskStyles[anomaly.riskLevel] || riskStyles['Faible'];

    return (
        <li className={`p-3 rounded-lg border ${style.bg} ${style.border}`}>
            <div className="flex justify-between items-start">
                <p className={`font-semibold text-sm ${style.text}`}>{anomaly.riskLevel}</p>
                <p className="text-sm font-medium text-gray-800">{anomaly.employeeName}</p>
            </div>
            <p className="text-sm text-gray-700 mt-1">{anomaly.description}</p>
        </li>
    );
};

const AnalysisResultView: React.FC<{analysis: PayrollAnalysis}> = ({ analysis }) => (
    <>
        <Card><p className="text-gray-800"><span className="font-bold text-[--org-color-main]">Synthèse IA :</span> {analysis.summary}</p></Card>
        <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <Card className="flex items-center"><div className="p-3 bg-red-100 rounded-full mr-4"><Icon.Clock className="h-6 w-6 text-red-600"/></div><div><p className="text-sm font-medium text-gray-500">Coût Total Heures Sup.</p><p className="text-2xl font-bold text-gray-800">{analysis.totalOvertimeCost.toLocaleString('fr-TN', { style: 'currency', currency: 'TND', minimumFractionDigits: 0 })}</p></div></Card>
            <Card className="flex items-center"><div className="p-3 bg-blue-100 rounded-full mr-4"><Icon.Users className="h-6 w-6 text-blue-600"/></div><div><p className="text-sm font-medium text-gray-500">Total Heures Sup.</p><p className="text-2xl font-bold text-gray-800">{analysis.totalOvertimeHours.toLocaleString('fr-FR')}h</p></div></Card>
            <Card className="flex items-center"><div className="p-3 bg-yellow-100 rounded-full mr-4"><Icon.Warning className="h-6 w-6 text-yellow-600"/></div><div><p className="text-sm font-medium text-gray-500">Anomalies</p><p className="text-2xl font-bold text-gray-800">{analysis.potentialAnomalies.length}</p></div></Card>
        </div>
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
             <Card>
                <h3 className="font-semibold text-gray-800 mb-2">Anomalies Potentielles</h3>
                <ul className="space-y-2 max-h-60 overflow-y-auto pr-2">
                    {analysis.potentialAnomalies.length > 0 ? analysis.potentialAnomalies.map((item, i) => <AnomalyItem key={i} anomaly={item} />) : <p className="text-sm text-gray-500">Aucune anomalie détectée.</p>}
                </ul>
             </Card>
             <Card>
                <h3 className="font-semibold text-gray-800 mb-2">Top 5 Employés (Heures Sup.)</h3>
                 <ul className="space-y-2">
                    {analysis.topOvertimeEmployees.map((item, i) => (
                        <li key={i} className="flex justify-between items-center p-2 rounded-lg hover:bg-gray-50">
                            <span className="font-medium text-sm text-gray-800">{item.employeeName}</span>
                            <div className="text-right">
                                <span className="font-bold text-sm text-[--org-color-main]">{item.overtimeHours}h</span>
                                <span className="text-xs text-gray-500 block">{item.overtimeCost.toLocaleString('fr-TN', { style: 'currency', currency: 'TND', minimumFractionDigits: 0})}</span>
                            </div>
                        </li>
                    ))}
                </ul>
             </Card>
        </div>
    </>
);


interface PayrollViewProps {
    onAddReport: (reportData: Omit<PayrollReport, 'id' | 'created_at' | 'organization_id'|'user_id'>) => Promise<void>;
    onDeleteReport: (reportId: string) => void;
    savedReports: PayrollReport[];
}

export const PayrollView: React.FC<PayrollViewProps> = ({ onAddReport, onDeleteReport, savedReports }) => {
    const [activeTab, setActiveTab] = useState<'new' | 'saved'>('new');
    const [file, setFile] = useState<File | null>(null);
    const [analysis, setAnalysis] = useState<PayrollAnalysis | null>(null);
    const [isAnalyzing, setIsAnalyzing] = useState(false);
    const [isSaving, setIsSaving] = useState(false);
    const [error, setError] = useState<string | null>(null);
    const [showCamera, setShowCamera] = useState(false);
    const [selectedReport, setSelectedReport] = useState<PayrollReport | null>(null);
    const [showPeriodInput, setShowPeriodInput] = useState(false);
    const [period, setPeriod] = useState(new Date().toLocaleDateString('fr-FR', { month: 'long', year: 'numeric' }));
    const { showNotification } = useNotification();
    
    const videoRef = useRef<HTMLVideoElement>(null);
    const canvasRef = useRef<HTMLCanvasElement>(null);
    const streamRef = useRef<MediaStream | null>(null);

    const cleanupStream = useCallback(() => {
        if (streamRef.current) {
            streamRef.current.getTracks().forEach(track => track.stop());
            streamRef.current = null;
        }
        if(videoRef.current) {
            videoRef.current.srcObject = null;
        }
    },[]);

    useEffect(() => {
        const startCamera = async () => {
            if (showCamera) {
                try {
                    const stream = await navigator.mediaDevices.getUserMedia({ video: { facingMode: 'environment' } });
                    if (videoRef.current) {
                        videoRef.current.srcObject = stream;
                        streamRef.current = stream;
                    }
                } catch (err) {
                    console.error("Error accessing camera:", err);
                    showNotification("Impossible d'accéder à la caméra. Vérifiez les autorisations.", 'error');
                    setShowCamera(false);
                }
            }
        };
        startCamera();
        return () => cleanupStream();
    }, [showCamera, showNotification, cleanupStream]);

    useEffect(() => {
        if (selectedReport && !savedReports.find(r => r.id === selectedReport.id)) {
            setSelectedReport(null);
        }
    }, [savedReports, selectedReport]);

    const handleProcessFile = (selectedFile: File | null) => {
        if (selectedFile) {
            setFile(selectedFile);
            setError(null);
            setAnalysis(null);
            showNotification("Fichier prêt pour l'analyse.", "success");
        } else {
            setError("Aucun fichier sélectionné.");
            showNotification("Aucun fichier sélectionné.", "error");
            setFile(null);
        }
    };
    
    const handleFileChange = (e: React.ChangeEvent<HTMLInputElement>) => {
        const selectedFile = e.target.files?.[0];
        handleProcessFile(selectedFile || null);
        e.target.value = ''; // Reset input
    };

    const handleCapture = () => {
        if (videoRef.current && canvasRef.current) {
            const video = videoRef.current;
            const canvas = canvasRef.current;
            canvas.width = video.videoWidth;
            canvas.height = video.videoHeight;
            const context = canvas.getContext('2d');
            if (context) {
                context.drawImage(video, 0, 0, video.videoWidth, video.videoHeight);
                canvas.toBlob((blob) => {
                    if (blob) {
                        const capturedFile = new File([blob], `scan-paie-${Date.now()}.jpg`, { type: 'image/jpeg' });
                        handleProcessFile(capturedFile);
                        setShowCamera(false);
                        cleanupStream();
                    }
                }, 'image/jpeg');
            }
        }
    };

    const handleAnalyze = useCallback(async () => {
        if (!file) {
            showNotification("Aucun fichier à analyser.", "error");
            return;
        }
        setIsAnalyzing(true);
        setError(null);
        setAnalysis(null);
        try {
            const result = await analyzePayrollData(file);
            setAnalysis(result);
            showNotification("Analyse IA terminée avec succès.", "success");
        } catch (err) {
            const message = getErrorMessage(err);
            setError(message);
            showNotification(message, "error");
        } finally {
            setIsAnalyzing(false);
        }
    }, [file, showNotification]);

    const handleConfirmSave = async () => {
        if (!file || !analysis) {
            showNotification("Aucune analyse à sauvegarder. Veuillez d'abord analyser un fichier.", "error");
            return;
        }
        if (!period.trim()) {
            showNotification("Veuillez entrer une période pour le rapport.", "error");
            return;
        }

        setIsSaving(true);
        try {
            await onAddReport({
                report_period: period,
                source_file_name: file.name,
                analysis_results: analysis
            });
            // Success case: reset UI and move to saved reports
            handleReset();
            setActiveTab('saved');
        } catch (err) {
            showNotification(`Erreur de sauvegarde du rapport: ${getErrorMessage(err)}`, 'error');
        } finally {
            setIsSaving(false);
        }
    };

    const handleReset = () => {
        setFile(null);
        setAnalysis(null);
        setError(null);
        cleanupStream();
        setShowCamera(false);
        setShowPeriodInput(false);
        setPeriod(new Date().toLocaleDateString('fr-FR', { month: 'long', year: 'numeric' }));
    };

    const renderNewAnalysisTab = () => (
        <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
            <div className="lg:col-span-1">
                <h2 className="text-xl font-semibold text-gray-700 mb-2">
                    {showCamera ? 'Scanner le Document' : 'Panneau de Contrôle'}
                </h2>
                 <Card className="p-4">
                    {showCamera ? (
                        // Camera UI
                         <div className="flex flex-col items-center">
                            <div className="w-full bg-black rounded-lg overflow-hidden relative border-2 border-gray-300">
                                <video ref={videoRef} autoPlay playsInline className="w-full h-auto" />
                                <div className="absolute inset-0 border-8 border-white/50 rounded-lg pointer-events-none opacity-50"></div>
                                <canvas ref={canvasRef} className="hidden" />
                            </div>
                            <div className="mt-6 flex justify-center space-x-4">
                                <button type="button" onClick={handleCapture} className="py-2 px-6 bg-[--org-color-main] text-white font-semibold rounded-lg shadow-md hover:bg-[--org-color-dark] flex items-center">
                                    <Icon.Camera className="h-5 w-5 mr-2" />
                                    Prendre la photo
                                </button>
                                <button type="button" onClick={() => setShowCamera(false)} className="py-2 px-4 bg-gray-200 text-gray-800 font-semibold rounded-lg hover:bg-gray-300">Annuler</button>
                            </div>
                        </div>
                    ) : (
                        // File Upload UI
                        <>
                            {!file && (
                                <div className="flex flex-col items-center justify-center p-6 border-2 border-gray-300 border-dashed rounded-lg bg-gray-50">
                                    <Icon.Upload className="h-12 w-12 text-gray-400" />
                                    <p className="mt-2 text-sm font-semibold text-gray-600">Importez votre document de paie</p>
                                    <p className="text-xs text-gray-500 mb-4">Formats acceptés : PDF, JPG, PNG</p>
                                    <div className="flex flex-col sm:flex-row items-center justify-center gap-3">
                                        <label htmlFor="file-upload" className="w-full sm:w-auto flex items-center justify-center py-2 px-4 bg-white text-[--org-color-main] font-semibold rounded-lg border border-[--org-color-main] hover:bg-gray-100 cursor-pointer transition-colors">
                                            <Icon.Upload className="h-5 w-5 mr-2" />
                                            Téléverser
                                            <input id="file-upload" name="file-upload" type="file" className="sr-only" onChange={handleFileChange} accept="image/*,.pdf" />
                                        </label>
                                        <button type="button" onClick={() => setShowCamera(true)} className="w-full sm:w-auto flex items-center justify-center py-2 px-4 bg-white text-[--org-color-main] font-semibold rounded-lg border border-[--org-color-main] hover:bg-gray-100 transition-colors">
                                            <Icon.Camera className="h-5 w-5 mr-2" />
                                            Scanner
                                        </button>
                                    </div>
                                </div>
                            )}
                            
                            {error && <p className="text-red-600 text-sm mt-2">{error}</p>}

                            {file && (
                                <div className="text-center p-4 bg-green-50 rounded-lg">
                                    <p className="font-semibold text-green-800">Fichier prêt: {file.name}</p>
                                    
                                    {showPeriodInput ? (
                                        <div className="mt-4 space-y-3">
                                            <div>
                                                <label htmlFor="report-period" className="block text-sm font-medium text-green-800 mb-1">Période du rapport</label>
                                                <input
                                                    type="text"
                                                    id="report-period"
                                                    value={period}
                                                    onChange={(e) => setPeriod(e.target.value)}
                                                    className="w-full max-w-xs mx-auto p-2 border border-green-300 rounded-lg focus:ring-[--org-color-main] focus:border-[--org-color-main]"
                                                    placeholder="Ex: Juin 2024"
                                                    autoFocus
                                                />
                                            </div>
                                            <div className="flex gap-2 justify-center flex-wrap">
                                                <button onClick={handleConfirmSave} disabled={isSaving} className="flex items-center justify-center py-2 px-4 bg-green-600 text-white font-semibold rounded-lg shadow-md hover:bg-green-700 disabled:bg-opacity-60">
                                                    {isSaving ? <><Spinner size="sm" color="white" /><span className="ml-2">Sauvegarde...</span></> : <><Icon.CheckCircle className="h-5 w-5 mr-2" /><span>Confirmer</span></>}
                                                </button>
                                                <button onClick={() => setShowPeriodInput(false)} disabled={isSaving} className="py-2 px-4 bg-gray-200 text-gray-700 font-semibold rounded-lg hover:bg-gray-300">
                                                    Annuler
                                                </button>
                                            </div>
                                        </div>
                                    ) : (
                                        <>
                                            <p className="text-sm text-green-700">Le document est prêt à être analysé ou sauvegardé.</p>
                                            <div className="mt-4 flex gap-2 justify-center flex-wrap">
                                                <button onClick={handleAnalyze} disabled={isAnalyzing} className="flex items-center justify-center py-2 px-4 bg-[--org-color-main] text-white font-semibold rounded-lg shadow-md hover:bg-[--org-color-dark] disabled:bg-opacity-60">
                                                    {isAnalyzing ? <><Spinner size="sm" color="white" /><span className="ml-2">Analyse...</span></> : <><Icon.Sparkles className="h-5 w-5 mr-2" /><span>Lancer l'Analyse</span></>}
                                                </button>
                                                {analysis && (
                                                     <button onClick={() => setShowPeriodInput(true)} className="flex items-center justify-center py-2 px-4 bg-green-600 text-white font-semibold rounded-lg shadow-md hover:bg-green-700 disabled:bg-opacity-60">
                                                        <Icon.ArchiveBox className="h-5 w-5 mr-2" /><span>Sauvegarder</span>
                                                    </button>
                                                )}
                                                <button onClick={handleReset} className="p-2 bg-gray-200 text-gray-700 rounded-lg hover:bg-gray-300"><Icon.Trash className="h-5 w-5"/></button>
                                            </div>
                                        </>
                                    )}
                                </div>
                            )}
                        </>
                    )}
                 </Card>
            </div>
            <div className="lg:col-span-2 space-y-4">
                {isAnalyzing && <div className="flex justify-center items-center h-96"><Spinner size="lg" /><p className="ml-4 text-lg">Analyse en cours...</p></div>}
                
                {!isAnalyzing && !analysis && (
                    <Card className="flex items-center justify-center h-96 text-center">
                        <div>
                        <Icon.Report className="h-16 w-16 text-gray-300 mx-auto mb-4"/>
                        <h3 className="font-semibold text-gray-500">En attente de données</h3>
                        <p className="text-sm text-gray-400">Importez un fichier pour commencer l'audit.</p>
                        </div>
                    </Card>
                )}
                {analysis && <AnalysisResultView analysis={analysis} />}
            </div>
        </div>
    );

    const renderSavedReportsTab = () => (
         <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
            <div className="lg:col-span-1">
                 <h2 className="text-xl font-semibold text-gray-700 mb-2">Rapports Archivés</h2>
                 <Card className="p-2 max-h-[70vh] overflow-y-auto">
                    {savedReports.length === 0 ? (
                        <p className="p-4 text-center text-gray-500">Aucun rapport sauvegardé.</p>
                    ) : (
                        <ul className="space-y-1">
                            {savedReports.map(report => (
                                <li key={report.id}>
                                    <button onClick={() => setSelectedReport(report)} className={`w-full text-left p-3 rounded-lg flex justify-between items-center ${selectedReport?.id === report.id ? 'bg-[--org-color-light]' : 'hover:bg-gray-100'}`}>
                                        <div>
                                            <p className="font-semibold text-gray-800">{report.report_period}</p>
                                            <p className="text-xs text-gray-500 truncate max-w-xs">{report.source_file_name}</p>
                                        </div>
                                        <Icon.ChevronRight className="h-5 w-5 text-gray-400" />
                                    </button>
                                </li>
                            ))}
                        </ul>
                    )}
                 </Card>
            </div>
            <div className="lg:col-span-2 space-y-4">
                {!selectedReport ? (
                     <Card className="flex items-center justify-center h-96 text-center">
                        <div>
                            <Icon.ArchiveBox className="h-16 w-16 text-gray-300 mx-auto mb-4"/>
                            <h3 className="font-semibold text-gray-500">Sélectionnez un rapport</h3>
                            <p className="text-sm text-gray-400">Choisissez un rapport dans la liste pour voir les détails.</p>
                        </div>
                    </Card>
                ) : (
                    <>
                        <div className="flex justify-between items-center">
                            <h2 className="text-xl font-semibold text-gray-700">Détails du rapport : {selectedReport.report_period}</h2>
                             <button
                                onClick={() => onDeleteReport(selectedReport.id)}
                                className="flex items-center text-sm font-semibold text-red-600 bg-red-100 py-1 px-3 rounded-lg hover:bg-red-200"
                            >
                                <Icon.Trash className="h-4 w-4 mr-2" />
                                Supprimer
                            </button>
                        </div>
                        <AnalysisResultView analysis={selectedReport.analysis_results as PayrollAnalysis} />
                    </>
                )}
            </div>
         </div>
    );

    return (
        <div>
            <div className="flex flex-col sm:flex-row justify-between items-center mb-4 pb-4 border-b border-gray-200 gap-4">
                <h1 className="text-3xl font-bold text-[--org-color-main] uppercase tracking-wide">Audit de la Paie & Heures Sup.</h1>
                <div className="flex items-center space-x-2 bg-gray-200/80 p-1 rounded-lg">
                    <TabButton label="Nouvelle Analyse" icon={<Icon.Plus />} isActive={activeTab === 'new'} onClick={() => setActiveTab('new')} />
                    <TabButton label="Rapports Sauvegardés" icon={<Icon.ArchiveBox />} isActive={activeTab === 'saved'} onClick={() => setActiveTab('saved')} />
                </div>
            </div>
            
            <div className="mb-8 p-4 bg-blue-50 border border-blue-200 rounded-lg flex items-start">
                <Icon.QuestionMarkCircle className="h-6 w-6 text-blue-500 flex-shrink-0 mr-3 mt-1" />
                <div>
                    <h3 className="font-semibold text-blue-800">Base de l'Audit IA</h3>
                    <p className="text-sm text-blue-700">
                        L'analyse est effectuée selon les règles du <strong>Code du Travail tunisien</strong>. Les taux de majoration pour heures supplémentaires appliqués par l'IA sont :
                    </p>
                    <ul className="list-disc list-inside text-sm text-blue-700 mt-2 space-y-1">
                        <li>Heures de jour : <strong className="font-bold">+25%</strong></li>
                        <li>Heures de nuit (21h-6h) : <strong className="font-bold">+50%</strong></li>
                        <li>Jours de repos / fériés : <strong className="font-bold">+100%</strong></li>
                    </ul>
                    <p className="text-xs text-blue-600 mt-2">
                        L'IA tente de distinguer les types d'heures. En l'absence de distinction sur le document, le taux de jour (+25%) est utilisé par défaut.
                    </p>
                </div>
            </div>

            {activeTab === 'new' && renderNewAnalysisTab()}
            {activeTab === 'saved' && renderSavedReportsTab()}
        </div>
    );
};