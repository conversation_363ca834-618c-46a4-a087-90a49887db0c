


import React from 'react';
import { Employee } from '../types';
import { Icon } from './shared/Icon';
import { AbsenceList } from './AbsenceList';
import type { Database } from '../services/supabaseClient';
import { Card } from './shared/Card';

type AbsenceRow = Database['public']['Tables']['absences']['Row'];

interface EmployeeProfileViewProps {
  employee: Employee;
  absences: AbsenceRow[];
  onBack: () => void;
  onEditEmployee: (employee: Employee) => void;
  onAddAbsence: () => void;
  storageBucketError: string | null;
}

export const EmployeeProfileView: React.FC<EmployeeProfileViewProps> = ({ employee, absences, onBack, onEditEmployee, onAddAbsence, storageBucketError }) => {
    const getInitials = (name: string) => name.split(' ').map(n => n[0]).join('').toUpperCase();
    
    return (
        <div>
            <div className="flex justify-between items-center mb-6">
                <button onClick={onBack} className="flex items-center text-sm font-semibold text-gray-600 hover:text-[--org-color-main]">
                    <Icon.ChevronLeft className="h-5 w-5 mr-1"/>
                    Retour aux employés
                </button>
            </div>

            {storageBucketError && (
                <div className="bg-red-100 border-l-4 border-red-500 text-red-700 p-4 mb-6 rounded-md shadow-md" role="alert">
                    <div className="flex">
                        <div className="py-1"><Icon.Warning className="h-6 w-6 text-red-500 mr-4"/></div>
                        <div>
                            <p className="font-bold">Action Requise: Configuration du Stockage</p>
                            <p className="text-sm">{storageBucketError}</p>
                        </div>
                    </div>
                </div>
            )}
            
            {/* Header */}
            <div className="bg-white p-6 rounded-xl shadow-sm border border-gray-200/80 mb-6">
                <div className="flex flex-col sm:flex-row justify-between items-start">
                    <div className="flex items-center space-x-4">
                        <div className="flex-shrink-0 h-20 w-20 rounded-full bg-[--org-color-light] flex items-center justify-center">
                            <span className="text-3xl font-bold text-[--org-color-main]">{getInitials(employee.name)}</span>
                        </div>
                        <div>
                            <h1 className="font-bold text-gray-800 text-2xl">{employee.name}</h1>
                            <p className="text-md text-gray-500">{employee.position}</p>
                            <span className="mt-1 mr-2 inline-block bg-blue-100 text-blue-800 text-xs font-semibold px-2.5 py-0.5 rounded-full">{employee.team}</span>
                        </div>
                    </div>
                    <div className="flex items-center space-x-2 mt-4 sm:mt-0">
                        <button onClick={() => onEditEmployee(employee)} className="p-2 text-sm font-semibold text-gray-700 bg-gray-100 rounded-lg hover:bg-gray-200 flex items-center"><Icon.Pencil className="w-4 h-4 mr-2" /> Modifier</button>
                    </div>
                </div>
            </div>

            {/* Main Content */}
            <div className="mt-8">
                <div className="flex justify-between items-center mb-4">
                    <h2 className="text-xl font-bold text-gray-700">Historique des Absences</h2>
                    <button
                        onClick={onAddAbsence}
                        className="flex items-center bg-[--org-color-main] text-white font-semibold py-2 px-4 rounded-lg shadow-md hover:bg-[--org-color-dark] transform transition-all duration-200 text-sm"
                    >
                        <Icon.Plus className="h-4 w-4 mr-2"/>
                        Analyser un certificat
                    </button>
                </div>
                <Card className="p-0">
                    <AbsenceList absences={absences} employeeName={employee.name} />
                </Card>
            </div>
        </div>
    );
};