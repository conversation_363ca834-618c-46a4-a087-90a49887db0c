<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>LICA Audit & Conseil</title>
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@400;500;600;700;800&display=swap" rel="stylesheet">
    <script src="https://cdn.tailwindcss.com"></script>
    <style>
        :root {
            --lica-blue: #0A2940;
            --lica-blue-dark: #072136;
            --lica-blue-light: #F0F5F9; /* Adjusted for a lighter feel */
            --font-family-sans: 'Inter', sans-serif;
        }
        body {
            font-family: var(--font-family-sans);
            background-color: var(--lica-blue-light);
        }
        .card-hover {
            transition: transform 0.2s ease-in-out, box-shadow 0.2s ease-in-out;
        }
        .card-hover:hover {
            transform: translateY(-4px);
            box-shadow: 0 10px 15px -3px rgb(0 0 0 / 0.07), 0 4px 6px -4px rgb(0 0 0 / 0.07);
        }
        @keyframes fadeInDown {
            from {
                opacity: 0;
                transform: translate(-50%, -20px);
            }
            to {
                opacity: 1;
                transform: translate(-50%, 0);
            }
        }
        .animate-fade-in-down {
            animation: fadeInDown 0.5s ease-out forwards;
        }

        /* Added for modal animations */
        @keyframes modal-content-fade-in {
            from {
                opacity: 0;
                transform: translateY(-20px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }
        .modal-content-animate {
            animation: modal-content-fade-in 0.3s ease-out forwards;
        }
    </style>
<script type="importmap">
{
  "imports": {
    "react/": "https://esm.sh/react@^19.1.1/",
    "react": "https://esm.sh/react@^19.1.1",
    "react-dom/": "https://esm.sh/react-dom@^19.1.1/",
    "@google/genai": "https://esm.sh/@google/genai@^1.11.0",
    "jspdf": "https://esm.sh/jspdf@^2.5.1",
    "jspdf-autotable": "https://esm.sh/jspdf-autotable@^3.8.2",
    "@supabase/supabase-js": "https://esm.sh/@supabase/supabase-js@^2.45.1",
    "@sentry/react": "https://esm.sh/@sentry/react@^8.20.0"
  }
}
</script>
<link rel="stylesheet" href="/index.css">
</head>
<body class="antialiased text-gray-800">
    <div id="root"></div>
    <script type="module" src="/index.tsx"></script>
</body>
</html>