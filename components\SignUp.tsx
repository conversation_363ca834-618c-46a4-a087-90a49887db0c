
import React, { useState } from 'react';
import { Icon } from './shared/Icon';
import { Spinner } from './shared/Spinner';
import { supabase } from '../services/supabaseClient';

interface SignUpProps {
  onSwitchToLogin: () => void;
}

export const SignUp: React.FC<SignUpProps> = ({ onSwitchToLogin }) => {
  const [email, setEmail] = useState('');
  const [password, setPassword] = useState('');
  const [error, setError] = useState('');
  const [isLoading, setIsLoading] = useState(false);
  const [showPassword, setShowPassword] = useState(false);

  const handleSignUp = async (e: React.FormEvent) => {
    e.preventDefault();
    setIsLoading(true);
    setError('');

    const { data, error: signUpError } = await supabase.auth.signUp({
      email: email,
      password: password,
    });

    setIsLoading(false);

    if (signUpError) {
      if (signUpError.message.includes('Password should be at least 6 characters')) {
        setError('Le mot de passe doit contenir au moins 6 caractères.');
      } else if (signUpError.message.includes('User already registered')) {
        setError("Cet email est déjà utilisé. Essayez de vous connecter ou utilisez un autre email.");
      } else {
        setError(`Erreur d'inscription: ${signUpError.message}`);
      }
      return;
    }

    if (data.user && !data.session) {
       alert("Inscription réussie ! Veuillez vérifier votre boîte de réception pour cliquer sur le lien de confirmation avant de vous connecter.");
       onSwitchToLogin();
    }
  };

  return (
    <div className="flex items-center justify-center min-h-screen bg-[--lica-blue-light]">
      <div className="w-full max-w-md mx-auto p-8">
        <div className="text-center mb-8">
            <Icon.LicaFullLogo className="h-20 w-auto mx-auto text-[--lica-blue]" />
        </div>
        
        <div className="bg-white rounded-xl shadow-lg p-8">
          <h2 className="text-2xl font-bold text-center text-gray-800 mb-1">
            Créer votre compte Auditeur
          </h2>
          <p className="text-center text-gray-500 mb-6">
            Un seul compte pour gérer tous vos clients.
          </p>
          
          <form onSubmit={handleSignUp} className="space-y-4">
            <div>
              <label htmlFor="email" className="block text-sm font-medium text-gray-700">Email de connexion</label>
              <input id="email" name="email" type="email" autoComplete="email" required value={email} onChange={(e) => setEmail(e.target.value)} className="w-full mt-1 px-3 py-2 border border-gray-300 rounded-lg shadow-sm focus:outline-none focus:ring-2 focus:ring-[--lica-blue] focus:border-transparent" placeholder="<EMAIL>"/>
            </div>
            <div>
              <label htmlFor="password"className="block text-sm font-medium text-gray-700">Mot de passe</label>
              <div className="mt-1 relative">
                <input id="password" name="password" type={showPassword ? 'text' : 'password'} required value={password} onChange={(e) => setPassword(e.target.value)} className="w-full px-3 py-2 border border-gray-300 rounded-lg shadow-sm focus:outline-none focus:ring-2 focus:ring-[--lica-blue] focus:border-transparent" placeholder="Minimum 6 caractères"/>
                <button type="button" onClick={() => setShowPassword(!showPassword)} className="absolute inset-y-0 right-0 pr-3 flex items-center text-gray-400 hover:text-gray-600" aria-label={showPassword ? 'Cacher' : 'Afficher'}>
                    {showPassword ? <Icon.EyeOff className="h-5 w-5" /> : <Icon.Eye className="h-5 w-5" />}
                </button>
              </div>
            </div>
            
            {error && (
                <div className="bg-red-50 border-l-4 border-red-400 p-3">
                    <div className="flex"><div className="flex-shrink-0"><Icon.Warning className="h-5 w-5 text-red-400" /></div><div className="ml-3"><p className="text-sm text-red-700">{error}</p></div></div>
                </div>
            )}

            <div className="pt-2">
              <button type="submit" disabled={isLoading} className="w-full flex justify-center py-3 px-4 border border-transparent rounded-lg shadow-sm text-sm font-medium text-white bg-[--lica-blue] hover:bg-[--lica-blue-dark] focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-[--lica-blue] disabled:bg-opacity-60 disabled:cursor-wait transition-colors">
                {isLoading ? <Spinner size="sm" color="white" /> : 'Créer le compte'}
              </button>
            </div>
          </form>

        </div>
        <p className="mt-6 text-center text-sm text-gray-600">
          Déjà un compte ?{' '}
          <button onClick={onSwitchToLogin} className="font-medium text-[--lica-blue] hover:text-[--lica-blue-dark]">
            Se connecter
          </button>
        </p>
        <p className="mt-4 text-center text-xs text-gray-500">
            &copy; 2024 LICA Audit & Conseil. Tous droits réservés.
        </p>
      </div>
    </div>
  );
};
