

import React, { useState } from 'react';
import { HRDocument, Employee, DocumentCategory, getDocumentCategoryLabel } from '../types';
import { Card } from './shared/Card';
import { Icon } from './shared/Icon';
import { AddOrEditDocumentModal } from './AddOrEditDocumentModal';
import { Spinner } from './shared/Spinner';

interface DocumentLibraryProps {
  documents: HRDocument[];
  employees: Employee[];
  addDocument: (docData: Omit<HRDocument, 'id' | 'uploadDate' | 'url' | 'storagePath' | 'organizationId'> & { file: File }) => Promise<void>;
  updateDocument: (docData: Omit<HRDocument, 'uploadDate' | 'url'> & { file?: File }) => Promise<void>;
  deleteDocument: (doc: HRDocument) => void;
  onAnalyze: (doc: HRDocument) => void;
  analyzingDocId: string | null;
  storageBucketError: string | null;
}

export const DocumentLibrary: React.FC<DocumentLibraryProps> = ({ documents, employees, addDocument, updateDocument, deleteDocument, onAnalyze, analyzingDocId, storageBucketError }) => {
  const [isModalOpen, setIsModalOpen] = useState(false);
  const [documentToEdit, setDocumentToEdit] = useState<HRDocument | undefined>(undefined);
  
  const getCategoryIcon = (category: DocumentCategory) => {
    switch (category) {
      case 'Contrat': return <Icon.DocumentText className="h-8 w-8 text-blue-500" />;
      case 'reglement_interne': return <Icon.Scale className="h-8 w-8 text-yellow-500" />;
      case 'Diplome': return <Icon.AcademicCap className="h-8 w-8 text-purple-500" />;
      case 'certificat_de_travail': return <Icon.ShieldCheck className="h-8 w-8 text-green-500" />;
      default: return <Icon.Document className="h-8 w-8 text-gray-500" />;
    }
  };

  const handleOpenAddModal = () => {
    setDocumentToEdit(undefined);
    setIsModalOpen(true);
  };
  
  const handleOpenEditModal = (doc: HRDocument) => {
    setDocumentToEdit(doc);
    setIsModalOpen(true);
  };

  const employeeMap = new Map(employees.map(e => [e.id, e.name]));

  return (
    <div>
      {storageBucketError && (
          <div className="bg-red-100 border-l-4 border-red-500 text-red-700 p-4 mb-6 rounded-md shadow-md" role="alert">
              <div className="flex">
                  <div className="py-1"><Icon.Warning className="h-6 w-6 text-red-500 mr-4"/></div>
                  <div>
                      <p className="font-bold">Erreur de Configuration Requise</p>
                      <p className="text-sm">{storageBucketError}</p>
                  </div>
              </div>
          </div>
      )}
      <div className="flex justify-between items-center mb-8 pb-4 border-b border-gray-200">
        <h1 className="text-3xl font-bold text-[--org-color-main] uppercase tracking-wide">Bibliothèque de Documents RH</h1>
         <button
            onClick={handleOpenAddModal}
            className="flex items-center bg-[--org-color-main] text-white font-semibold py-2 px-5 rounded-lg shadow-md hover:bg-[--org-color-dark] hover:scale-105 transform transition-all duration-200 uppercase tracking-wider"
        >
            <Icon.Plus className="h-5 w-5 mr-2"/>
            Ajouter un document
        </button>
      </div>

      <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6">
        {documents.map(doc => {
            const isAnalyzing = analyzingDocId === doc.id;
            const canAnalyze: DocumentCategory[] = ['Contrat', 'reglement_interne', 'Diplome', 'certificat_de_travail'];
            return (
          <Card key={doc.id} className="flex flex-col">
            <div className="flex-grow p-5">
              <div className="flex items-start justify-between mb-4">
                <div className="p-3 bg-gray-100 rounded-lg mr-4">
                  {getCategoryIcon(doc.category)}
                </div>
                <span className="text-sm font-semibold bg-gray-200 text-gray-700 px-2 py-1 rounded">{getDocumentCategoryLabel(doc.category)}</span>
              </div>
              <h3 className="font-bold text-gray-800 mb-2 truncate" title={doc.name}>{doc.name}</h3>
              <p className="text-xs text-gray-500">Ajouté le: {new Date(doc.uploadDate).toLocaleDateString('fr-FR')}</p>
              {doc.employeeId && <p className="text-xs text-gray-500 mt-1">Employé: <span className="font-medium">{employeeMap.get(doc.employeeId) || 'Inconnu'}</span></p>}
            </div>
            <div className="border-t border-gray-200 p-2 bg-gray-50/70 flex justify-end items-center space-x-1">
                {canAnalyze.includes(doc.category) && (
                    <button
                      onClick={() => onAnalyze(doc)}
                      disabled={isAnalyzing}
                      className="p-2 text-gray-600 hover:bg-purple-100 hover:text-purple-700 rounded-md transition-colors disabled:cursor-wait"
                      title="Analyser la conformité avec l'IA"
                    >
                      {isAnalyzing ? <Spinner size="sm" /> : <Icon.Sparkles className="w-4 h-4" />}
                    </button>
                )}
                <a href={doc.url} target="_blank" rel="noopener noreferrer" className="p-2 text-gray-600 hover:bg-blue-100 hover:text-blue-700 rounded-md transition-colors" title="Télécharger"><Icon.Download className="w-4 h-4" /></a>
                <button onClick={() => handleOpenEditModal(doc)} className="p-2 text-gray-600 hover:bg-yellow-100 hover:text-yellow-700 rounded-md transition-colors" title="Modifier"><Icon.Pencil className="w-4 h-4" /></button>
                <button onClick={() => deleteDocument(doc)} className="p-2 text-gray-600 hover:bg-red-100 hover:text-red-700 rounded-md transition-colors" title="Supprimer"><Icon.Trash className="w-4 h-4" /></button>
            </div>
          </Card>
        )})}
        {documents.length === 0 && (
            <div className="col-span-full text-center py-16">
                 <Icon.Document className="h-16 w-16 text-gray-400 mx-auto mb-4"/>
                <h3 className="text-xl font-semibold text-gray-700">Aucun document pour le moment</h3>
                <p className="text-gray-500 mt-2">Cliquez sur "Ajouter un document" pour commencer.</p>
            </div>
        )}
      </div>

      {isModalOpen && (
        <AddOrEditDocumentModal
          onClose={() => setIsModalOpen(false)}
          addDocument={addDocument}
          updateDocument={updateDocument}
          documentToEdit={documentToEdit}
          employees={employees}
        />
      )}
    </div>
  );
};