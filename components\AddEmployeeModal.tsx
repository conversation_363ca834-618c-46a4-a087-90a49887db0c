import React, { useState, useEffect } from 'react';
import { Employee } from '../types';
import { Icon } from './shared/Icon';
import { Spinner } from './shared/Spinner';

interface AddEmployeeModalProps {
  onClose: () => void;
  addEmployee: (newEmployee: Omit<Employee, 'id' | 'organizationId'>) => Promise<void>;
  updateEmployee?: (employee: Employee) => Promise<void>;
  employeeToEdit?: Employee;
}

export const AddEmployeeModal: React.FC<AddEmployeeModalProps> = ({ onClose, addEmployee, updateEmployee, employeeToEdit }) => {
  const [name, setName] = useState('');
  const [position, setPosition] = useState('');
  const [team, setTeam] = useState('');
  const [contractStartDate, setContractStartDate] = useState<string | null>(null);
  const [contractEndDate, setContractEndDate] = useState<string | null>(null);
  const [isSubmitting, setIsSubmitting] = useState(false);

  const isEditMode = !!employeeToEdit;

  useEffect(() => {
    if (isEditMode) {
      setName(employeeToEdit.name);
      setPosition(employeeToEdit.position);
      setTeam(employeeToEdit.team);
      setContractStartDate(employeeToEdit.contractStartDate);
      setContractEndDate(employeeToEdit.contractEndDate);
    }
  }, [employeeToEdit, isEditMode]);

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    if (!name || !position || !team) {
      alert("Veuillez remplir tous les champs.");
      return;
    }
    setIsSubmitting(true);

    const employeeData = {
      name,
      position,
      team,
      contractStartDate: contractStartDate || null,
      contractEndDate: contractEndDate || null,
    };
    
    try {
        if (isEditMode && updateEmployee && employeeToEdit) {
            await updateEmployee({ ...employeeToEdit, ...employeeData });
        } else {
            await addEmployee(employeeData);
        }
        onClose();
    } catch (error) {
        console.error("Failed to save employee", error);
        alert("Une erreur est survenue lors de la sauvegarde de l'employé.");
    } finally {
        setIsSubmitting(false);
    }
  };

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex justify-center items-center z-50">
      <div className="bg-white rounded-xl shadow-2xl w-full max-w-md p-8 m-4 max-h-[90vh] overflow-y-auto">
        <div className="flex justify-between items-center mb-6">
          <h2 className="text-2xl font-bold text-gray-800 uppercase tracking-wide">
            {isEditMode ? 'Modifier un employé' : 'Ajouter un employé'}
          </h2>
          <button onClick={onClose} className="text-gray-400 hover:text-gray-600">
            <Icon.Close className="h-6 w-6" />
          </button>
        </div>
        <form onSubmit={handleSubmit}>
          <div className="space-y-4">
            <div>
              <label htmlFor="name" className="block text-sm font-medium text-gray-700 mb-1">Nom complet</label>
              <input type="text" id="name" value={name} onChange={e => setName(e.target.value)} required className="w-full p-2 border border-gray-300 rounded-lg focus:ring-[--org-color-main] focus:border-[--org-color-main]" placeholder="Ex: Jean Dupont" />
            </div>
            <div>
              <label htmlFor="position" className="block text-sm font-medium text-gray-700 mb-1">Poste</label>
              <input type="text" id="position" value={position} onChange={e => setPosition(e.target.value)} required className="w-full p-2 border border-gray-300 rounded-lg focus:ring-[--org-color-main] focus:border-[--org-color-main]" placeholder="Ex: Développeur" />
            </div>
            <div>
              <label htmlFor="team" className="block text-sm font-medium text-gray-700 mb-1">Équipe</label>
              <input type="text" id="team" value={team} onChange={e => setTeam(e.target.value)} required className="w-full p-2 border border-gray-300 rounded-lg focus:ring-[--org-color-main] focus:border-[--org-color-main]" placeholder="Ex: Tech"/>
            </div>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                 <div>
                    <label htmlFor="contract-start-date" className="block text-sm font-medium text-gray-700 mb-1">Date début contrat</label>
                    <input type="date" id="contract-start-date" value={contractStartDate || ''} onChange={e => setContractStartDate(e.target.value)} className="w-full p-2 border border-gray-300 rounded-lg focus:ring-[--org-color-main] focus:border-[--org-color-main]" />
                </div>
                <div>
                    <label htmlFor="contract-end-date" className="block text-sm font-medium text-gray-700 mb-1">Date fin contrat (Optionnel)</label>
                    <input type="date" id="contract-end-date" value={contractEndDate || ''} onChange={e => setContractEndDate(e.target.value)} className="w-full p-2 border border-gray-300 rounded-lg focus:ring-[--org-color-main] focus:border-[--org-color-main]" />
                </div>
            </div>
          </div>
          <div className="mt-8 flex justify-end space-x-4">
            <button type="button" onClick={onClose} className="py-2 px-4 bg-gray-200 text-gray-800 font-semibold rounded-lg hover:bg-gray-300">Annuler</button>
            <button type="submit" disabled={isSubmitting} className="py-2 px-6 bg-[--org-color-main] text-white font-semibold rounded-lg shadow-md hover:bg-[--org-color-dark] disabled:bg-opacity-60 disabled:cursor-not-allowed flex items-center">
              {isSubmitting ? <><Spinner size="sm" color="white"/> <span className="ml-2">Sauvegarde...</span></> : (isEditMode ? 'Sauvegarder' : 'Ajouter l’employé')}
            </button>
          </div>
        </form>
      </div>
    </div>
  );
};