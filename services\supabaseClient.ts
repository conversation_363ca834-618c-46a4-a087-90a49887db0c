

import { createClient } from '@supabase/supabase-js';

// The hardcoded Supabase credentials.
const supabaseUrl = 'https://paiipnnikynsvmecrkyi.supabase.co';
const supabaseAnonKey = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InBhaWlwbm5pa3luc3ZtZWNya3lpIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NTM5MTQ1OTIsImV4cCI6MjA2OTQ5MDU5Mn0.cNB2iadrmBhU4rrRqNnY0aWs-j3SsOs1P9TiI7IUl04';

if (!supabaseUrl || !supabaseAnonKey) {
  throw new Error("Supabase URL and Anon Key must be provided.");
}

export interface Database {
  public: {
    Tables: {
      organizations: {
        Row: {
          id: string
          name: string
          created_at: string
          user_id: string
        }
        Insert: {
          id?: string
          name: string
          created_at?: string
          user_id: string
        }
        Update: {
          id?: string
          name?: string
          created_at?: string
          user_id?: string
        }
      }
      employees: {
        Row: {
          id: string
          name: string
          position: string
          team: string
          organization_id: string
          contract_start_date: string | null
          contract_end_date: string | null
        }
        Insert: {
          id?: string
          name: string
          position: string
          team: string
          organization_id: string
          contract_start_date?: string | null
          contract_end_date?: string | null
        }
        Update: {
          id?: string
          name?: string
          position?: string
          team?: string
          organization_id?: string
          contract_start_date?: string | null
          contract_end_date?: string | null
        }
      }
      absences: {
        Row: {
          id: string
          employee_id: string
          start_date: string
          end_date: string
          reason: string
          justified: boolean
          organization_id: string
          attachment_url: string | null
          ocr_text: string | null
          doctor_name: string | null
          extracted_reason: string | null
          extracted_start_date: string | null
          extracted_end_date: string | null
          extracted_patient_name: string | null
          stamp_detected: boolean | null
        }
        Insert: {
          id?: string
          employee_id: string
          start_date: string
          end_date: string
          reason: string
          justified: boolean
          organization_id: string
          attachment_url?: string | null
          ocr_text?: string | null
          doctor_name?: string | null
          extracted_reason?: string | null
          extracted_start_date?: string | null
          extracted_end_date?: string | null
          extracted_patient_name?: string | null
          stamp_detected?: boolean | null
        }
        Update: {
          id?: string
          employee_id?: string
          start_date?: string
          end_date?: string
          reason?: string
          justified?: boolean
          organization_id?: string
          attachment_url?: string | null
          ocr_text?: string | null
          doctor_name?: string | null
          extracted_reason?: string | null
          extracted_start_date?: string | null
          extracted_end_date?: string | null
          extracted_patient_name?: string | null
          stamp_detected?: boolean | null
        }
      }
      hr_documents: {
        Row: {
          id: string
          name: string
          category: "Contrat" | "reglement_interne" | "Diplome" | "certificat_de_travail"
          upload_date: string
          storage_path: string
          organization_id: string
          employee_id: string | null
        }
        Insert: {
          id?: string
          name: string
          category: "Contrat" | "reglement_interne" | "Diplome" | "certificat_de_travail"
          upload_date?: string
          storage_path: string
          organization_id: string
          employee_id?: string | null
        }
        Update: {
          id?: string
          name?: string
          category?: "Contrat" | "reglement_interne" | "Diplome" | "certificat_de_travail"
          upload_date?: string
          storage_path?: string
          organization_id?: string
          employee_id?: string | null
        }
      }
      payroll_reports: {
        Row: {
          id: string
          created_at: string
          organization_id: string
          user_id: string
          report_period: string
          source_file_name: string
          analysis_results: any
        }
        Insert: {
          id?: string
          created_at?: string
          organization_id: string
          user_id: string
          report_period: string
          source_file_name: string
          analysis_results: any
        }
        Update: {
          id?: string
          created_at?: string
          organization_id?: string
          user_id?: string
          report_period?: string
          source_file_name?: string
          analysis_results?: any
        }
      }
      monthly_absence_reports: {
        Row: {
          id: string
          created_at: string
          organization_id: string
          user_id: string
          report_period: string
          month: number
          year: number
          report_data: any
        }
        Insert: {
          id?: string
          created_at?: string
          organization_id: string
          user_id: string
          report_period: string
          month: number
          year: number
          report_data: any
        }
        Update: {
          id?: string
          created_at?: string
          organization_id?: string
          user_id?: string
          report_period?: string
          month?: number
          year?: number
          report_data?: any
        }
      }
      candidate_audits: {
        Row: {
          id: string
          created_at: string
          organization_id: string
          user_id: string
          candidate_name: string
          source_file_name: string
          storage_path: string
          status: "Conforme" | "Non Conforme" | "En cours" | "Échec"
          analysis_report: any | null
        }
        Insert: {
          id?: string
          created_at?: string
          organization_id: string
          user_id: string
          candidate_name: string
          source_file_name: string
          storage_path: string
          status: "Conforme" | "Non Conforme" | "En cours" | "Échec"
          analysis_report?: any | null
        }
        Update: {
          id?: string
          status?: "Conforme" | "Non Conforme" | "En cours" | "Échec"
          analysis_report?: any | null
          candidate_name?: string
        }
      }
    }
    Views: {
      [_ in never]: never
    }
    Functions: {
      delete_organization_and_data: {
        Args: {
          org_id: string
        }
        Returns: undefined
      }
    }
    CompositeTypes: {
      [_ in never]: never
    }
  }
}


export const supabase = createClient<Database>(supabaseUrl, supabaseAnonKey);