import React, { useState, useCallback } from 'react';
import { Employee } from '../types';
import { Icon } from './shared/Icon';
import { Spinner } from './shared/Spinner';
import { useNotification } from '../hooks/useNotification';

interface BatchDocumentModalProps {
  employee: Employee;
  onClose: () => void;
  onBatchUpload: (files: File[], employeeId: string) => Promise<{ success: boolean; name: string; error?: string }[]>;
}

type FileStatus = 'pending' | 'processing' | 'success' | 'error';

interface FileProgress {
  file: File;
  status: FileStatus;
  message?: string;
}

export const BatchDocumentModal: React.FC<BatchDocumentModalProps> = ({ employee, onClose, onBatchUpload }) => {
  const [files, setFiles] = useState<FileProgress[]>([]);
  const [isProcessing, setIsProcessing] = useState(false);
  const { showNotification } = useNotification();

  const handleFileSelect = (selectedFiles: FileList | null) => {
    if (selectedFiles) {
      const newFiles: FileProgress[] = Array.from(selectedFiles).map(file => ({
        file,
        status: 'pending',
      }));
      setFiles(prev => [...prev, ...newFiles]);
    }
  };

  const handleDrop = (e: React.DragEvent<HTMLDivElement>) => {
    e.preventDefault();
    e.stopPropagation();
    handleFileSelect(e.dataTransfer.files);
  };

  const handleSubmit = async () => {
    if (files.length === 0) {
      showNotification('Veuillez ajouter des fichiers à téléverser.', 'error');
      return;
    }
    
    setIsProcessing(true);
    setFiles(files.map(f => ({ ...f, status: 'processing' })));

    const filesToUpload = files.map(f => f.file);
    const results = await onBatchUpload(filesToUpload, employee.id);

    setFiles(currentFiles => 
        currentFiles.map(fp => {
            const result = results.find(r => r.name === fp.file.name);
            if(result) {
                return {
                    ...fp,
                    status: result.success ? 'success' : 'error',
                    message: result.error
                };
            }
            // If a file wasn't processed (e.g., batch was stopped), mark it as pending
            const processedFile = results.find(r => r.name === fp.file.name);
            if(!processedFile) {
                return {...fp, status: 'pending' };
            }
            return fp;
        })
    );

    setIsProcessing(false);
  };
  
  const getStatusIcon = (status: FileStatus) => {
      switch (status) {
          case 'processing': return <Spinner size="sm" />;
          case 'success': return <Icon.CheckCircle className="h-5 w-5 text-green-500" />;
          case 'error': return <Icon.Warning className="h-5 w-5 text-red-500" />;
          default: return <Icon.Clock className="h-5 w-5 text-gray-400" />;
      }
  }

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex justify-center items-center z-50 p-4">
      <div className="bg-white rounded-xl shadow-2xl w-full max-w-2xl p-8 m-4 max-h-[90vh] flex flex-col">
        <div className="flex justify-between items-center mb-6">
          <div>
            <h2 className="text-2xl font-bold text-gray-800 uppercase tracking-wide">Documents par lot (IA)</h2>
            <p className="text-sm text-gray-500">Pour : <span className="font-semibold">{employee.name}</span></p>
          </div>
          <button onClick={onClose} className="text-gray-400 hover:text-gray-600">
            <Icon.Close className="h-6 w-6" />
          </button>
        </div>
        
        <div className="flex-grow overflow-y-auto pr-2">
            <div 
                onDrop={handleDrop}
                onDragOver={(e) => e.preventDefault()}
                className="mt-1 flex justify-center px-6 pt-8 pb-8 border-2 border-gray-300 border-dashed rounded-md"
            >
                <div className="space-y-1 text-center">
                    <Icon.Upload className="mx-auto h-12 w-12 text-gray-400" />
                    <div className="flex text-sm text-gray-600">
                        <label htmlFor="batch-file-upload" className="relative cursor-pointer bg-white rounded-md font-medium text-[--org-color-main] hover:text-[--org-color-dark] focus-within:outline-none">
                            <span>Téléversez des fichiers</span>
                            <input id="batch-file-upload" name="batch-file-upload" type="file" multiple className="sr-only" onChange={(e) => handleFileSelect(e.target.files)} disabled={isProcessing}/>
                        </label>
                        <p className="pl-1">ou glissez-déposez</p>
                    </div>
                     <p className="text-xs text-gray-500">L'IA déterminera la catégorie de chaque document.</p>
                </div>
            </div>

            {files.length > 0 && (
                <div className="mt-6">
                    <h3 className="text-lg font-medium text-gray-800 mb-2">Fichiers à traiter ({files.length})</h3>
                    <ul className="space-y-2 max-h-60 overflow-y-auto bg-gray-50 p-3 rounded-lg border">
                        {files.map(({ file, status, message }, index) => (
                            <li key={index} className="flex items-center justify-between p-2 bg-white rounded-md shadow-sm">
                                <div className="flex items-center truncate">
                                    <span className="mr-3">{getStatusIcon(status)}</span>
                                    <div className="truncate">
                                        <p className="text-sm font-medium text-gray-800 truncate" title={file.name}>{file.name}</p>
                                         {message && <p className="text-xs text-red-600 truncate">{message}</p>}
                                    </div>
                                </div>
                                <button
                                    onClick={() => setFiles(files.filter((_, i) => i !== index))}
                                    className="ml-4 text-gray-400 hover:text-red-500"
                                    disabled={isProcessing}
                                >
                                    <Icon.Close className="h-4 w-4" />
                                </button>
                            </li>
                        ))}
                    </ul>
                </div>
            )}
        </div>
        
        <div className="mt-8 flex justify-end space-x-4">
          <button type="button" onClick={onClose} className="py-2 px-4 bg-gray-200 text-gray-800 font-semibold rounded-lg hover:bg-gray-300">
            Fermer
          </button>
          <button 
            type="button" 
            onClick={handleSubmit} 
            disabled={isProcessing || files.length === 0} 
            className="py-2 px-6 bg-[--org-color-main] text-white font-semibold rounded-lg shadow-md hover:bg-[--org-color-dark] disabled:bg-opacity-60 disabled:cursor-not-allowed flex items-center"
            >
            {isProcessing ? <><Spinner size="sm" color="white"/> <span className="ml-2">Traitement...</span></> : 'Lancer le traitement'}
          </button>
        </div>
      </div>
    </div>
  );
};