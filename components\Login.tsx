
import React, { useState } from 'react';
import { Icon } from './shared/Icon';
import { Spinner } from './shared/Spinner';
import { supabase } from '../services/supabaseClient';

interface LoginProps {
  onSwitchToSignUp: () => void;
}

export const Login: React.FC<LoginProps> = ({ onSwitchToSignUp }) => {
  const [email, setEmail] = useState('');
  const [password, setPassword] = useState('');
  const [error, setError] = useState('');
  const [isLoading, setIsLoading] = useState(false);
  const [showPassword, setShowPassword] = useState(false);

  const handleLogin = async (e: React.FormEvent) => {
    e.preventDefault();
    setIsLoading(true);
    setError('');

    const { error } = await supabase.auth.signInWithPassword({
        email: email,
        password: password,
    });

    if (error) {
        if (error.message === 'Invalid login credentials') {
            setError('Email ou mot de passe incorrect.');
        } else {
            setError('Une erreur est survenue. Veuillez réessayer.');
        }
    }
    // On success, the onAuthStateChange listener in App.tsx will handle navigation.
    
    setIsLoading(false);
  };


  return (
    <div className="flex items-center justify-center min-h-screen bg-[--lica-blue-light]">
      <div className="w-full max-w-md mx-auto p-8">
        <div className="text-center mb-8">
            <Icon.LicaFullLogo className="h-20 w-auto mx-auto text-[--lica-blue]" />
        </div>
        
        <div className="bg-white rounded-xl shadow-lg p-8">
          <h2 className="text-2xl font-bold text-center text-gray-800 mb-1">
            Connexion
          </h2>
          <p className="text-center text-gray-500 mb-6">
            Accédez à votre tableau de bord d'audit.
          </p>
          
          <form onSubmit={handleLogin} className="space-y-4">
            <div>
              <label htmlFor="email" className="block text-sm font-medium text-gray-700">
                Email
              </label>
              <div className="mt-1">
                <input
                  id="email"
                  name="email"
                  type="email"
                  autoComplete="email"
                  required
                  value={email}
                  onChange={(e) => setEmail(e.target.value)}
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg shadow-sm focus:outline-none focus:ring-2 focus:ring-[--lica-blue] focus:border-transparent"
                  placeholder="<EMAIL>"
                />
              </div>
            </div>

            <div>
              <label htmlFor="password"className="block text-sm font-medium text-gray-700">
                Mot de passe
              </label>
              <div className="mt-1 relative">
                <input
                  id="password"
                  name="password"
                  type={showPassword ? 'text' : 'password'}
                  autoComplete="current-password"
                  required
                  value={password}
                  onChange={(e) => setPassword(e.target.value)}
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg shadow-sm focus:outline-none focus:ring-2 focus:ring-[--lica-blue] focus:border-transparent"
                  placeholder="Votre mot de passe"
                />
                <button
                    type="button"
                    onClick={() => setShowPassword(!showPassword)}
                    className="absolute inset-y-0 right-0 pr-3 flex items-center text-gray-400 hover:text-gray-600"
                    aria-label={showPassword ? "Cacher le mot de passe" : "Afficher le mot de passe"}
                >
                    {showPassword ? <Icon.EyeOff className="h-5 w-5" /> : <Icon.Eye className="h-5 w-5" />}
                </button>
              </div>
            </div>
            
            {error && (
                <div className="bg-red-50 border-l-4 border-red-400 p-3">
                    <div className="flex">
                        <div className="flex-shrink-0">
                           <Icon.Warning className="h-5 w-5 text-red-400" />
                        </div>
                        <div className="ml-3">
                            <p className="text-sm text-red-700">{error}</p>
                        </div>
                    </div>
                </div>
            )}

            <div className="pt-2">
              <button
                type="submit"
                disabled={isLoading}
                className="w-full flex justify-center py-3 px-4 border border-transparent rounded-lg shadow-sm text-sm font-medium text-white bg-[--lica-blue] hover:bg-[--lica-blue-dark] focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-[--lica-blue] disabled:bg-opacity-60 disabled:cursor-wait transition-colors"
              >
                {isLoading ? <Spinner size="sm" color="white" /> : 'Se connecter'}
              </button>
            </div>
          </form>

        </div>
        <p className="mt-6 text-center text-sm text-gray-600">
          Pas encore de compte ?{' '}
          <button onClick={onSwitchToSignUp} className="font-medium text-[--lica-blue] hover:text-[--lica-blue-dark]">
            S'inscrire
          </button>
        </p>
        <p className="mt-4 text-center text-xs text-gray-500">
            &copy; 2024 LICA Audit & Conseil. Tous droits réservés.
        </p>
      </div>
    </div>
  );
};
