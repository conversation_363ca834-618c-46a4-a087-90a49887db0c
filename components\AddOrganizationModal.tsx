import React, { useState, useEffect } from 'react';
import { Icon } from './shared/Icon';
import { Spinner } from './shared/Spinner';
import type { Organization } from '../types';

interface AddOrganizationModalProps {
  onClose: () => void;
  onAdd: (name: string) => Promise<void>;
  onUpdate: (orgData: { id: string, name: string }) => Promise<void>;
  organizationToEdit?: Organization | null;
}

export const AddOrganizationModal: React.FC<AddOrganizationModalProps> = ({ onClose, onAdd, onUpdate, organizationToEdit }) => {
  const [name, setName] = useState('');
  const [isSubmitting, setIsSubmitting] = useState(false);
  const isEditMode = !!organizationToEdit;

  useEffect(() => {
    if (organizationToEdit) {
      setName(organizationToEdit.name);
    }
  }, [organizationToEdit]);

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    if (!name.trim()) {
      alert("Le nom de l'organisation ne peut pas être vide.");
      return;
    }
    setIsSubmitting(true);
    try {
      if (isEditMode && organizationToEdit) {
        await onUpdate({ id: organizationToEdit.id, name });
      } else {
        await onAdd(name);
      }
      onClose();
    } catch (error) {
      console.error("Failed to save organization", error);
    } finally {
      setIsSubmitting(false);
    }
  };

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex justify-center items-center z-50 p-4">
      <div className="bg-white rounded-xl shadow-2xl w-full max-w-md p-8 m-4">
        <div className="flex justify-between items-center mb-6">
          <h2 className="text-2xl font-bold text-gray-800 uppercase tracking-wide">
            {isEditMode ? "Modifier l'Organisation" : 'Nouvelle Organisation'}
          </h2>
          <button onClick={onClose} className="text-gray-400 hover:text-gray-600">
            <Icon.Close className="h-6 w-6" />
          </button>
        </div>
        <form onSubmit={handleSubmit}>
          <div className="space-y-4">
            <div>
              <label htmlFor="org-name" className="block text-sm font-medium text-gray-700 mb-1">
                Nom de l'organisation cliente
              </label>
              <input
                type="text"
                id="org-name"
                value={name}
                onChange={(e) => setName(e.target.value)}
                required
                className="w-full p-2 border border-gray-300 rounded-lg focus:ring-[--lica-blue] focus:border-[--lica-blue]"
                placeholder="Ex: Client S.A."
                autoFocus
              />
            </div>
          </div>
          <div className="mt-8 flex justify-end space-x-4">
            <button
              type="button"
              onClick={onClose}
              className="py-2 px-4 bg-gray-200 text-gray-800 font-semibold rounded-lg hover:bg-gray-300"
            >
              Annuler
            </button>
            <button
              type="submit"
              disabled={isSubmitting}
              className="py-2 px-6 bg-[--lica-blue] text-white font-semibold rounded-lg shadow-md hover:bg-[--lica-blue-dark] disabled:bg-[--lica-blue]/50 disabled:cursor-not-allowed flex items-center"
            >
              {isSubmitting ? (
                <>
                  <Spinner size="sm" color="white" /> <span className="ml-2">{isEditMode ? 'Sauvegarde...' : 'Ajout...'}</span>
                </>
              ) : (
                isEditMode ? 'Sauvegarder' : 'Ajouter'
              )}
            </button>
          </div>
        </form>
      </div>
    </div>
  );
};