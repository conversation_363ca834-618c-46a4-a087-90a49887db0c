import React, { useState } from 'react';
import { Employee } from '../types';
import { Icon } from './shared/Icon';
import { Spinner } from './shared/Spinner';
import { useNotification } from '../hooks/useNotification';
import { getErrorMessage } from '../services/errorService';
import type { Database } from '../services/supabaseClient';
import type { MedicalCertificateAnalysis } from '../services/geminiService';

type AbsenceInsert = Database['public']['Tables']['absences']['Insert'];

interface AddAbsenceModalProps {
  onClose: () => void;
  employee: Employee;
  onAddAbsence: (absenceData: Omit<AbsenceInsert, 'id' | 'organization_id' | 'employee_id' | 'attachment_url'>, file: File) => Promise<void>;
  analyzeCertificate: (file: File) => Promise<MedicalCertificateAnalysis>;
}

export const AddAbsenceModal: React.FC<AddAbsenceModalProps> = ({ onClose, employee, onAddAbsence, analyzeCertificate }) => {
    const [file, setFile] = useState<File | null>(null);
    const [isAnalyzing, setIsAnalyzing] = useState(false);
    const [isSubmitting, setIsSubmitting] = useState(false);
    const [analysisResult, setAnalysisResult] = useState<MedicalCertificateAnalysis | null>(null);
    const [conformity, setConformity] = useState<{
        patientNameMatch: boolean | null;
        dateConsistent: boolean | null;
    }>({ patientNameMatch: null, dateConsistent: null });


    const [startDate, setStartDate] = useState('');
    const [endDate, setEndDate] = useState('');
    const [reason, setReason] = useState('');
    const [isJustified, setIsJustified] = useState(true);

    const { showNotification } = useNotification();

    const handleFileSelected = async (selectedFile: File | null) => {
        if (!selectedFile) return;
        setFile(selectedFile);
        setIsAnalyzing(true);
        setAnalysisResult(null);
        setConformity({ patientNameMatch: null, dateConsistent: null });

        try {
            const result = await analyzeCertificate(selectedFile);
            setAnalysisResult(result);
            setStartDate(result.startDate || '');
            setEndDate(result.endDate || '');
            setReason(result.reason || 'Motif non extrait par l\'IA');

            const patientNameMatch = result.patientName ? 
                (employee.name.toLowerCase().includes(result.patientName.toLowerCase()) || 
                 result.patientName.toLowerCase().includes(employee.name.toLowerCase()))
                : false;
            const dateConsistent = result.startDate && result.endDate ? new Date(result.startDate) <= new Date(result.endDate) : false;

            setConformity({ patientNameMatch, dateConsistent });
            showNotification('Analyse IA terminée. Veuillez vérifier les données et la conformité.', 'success');
        } catch(err) {
            showNotification(`Erreur d'analyse: ${getErrorMessage(err)}`, 'error');
        } finally {
            setIsAnalyzing(false);
        }
    };

    const handleSubmit = async (e: React.FormEvent) => {
        e.preventDefault();
        if (!file || !startDate || !endDate || !reason) {
            showNotification('Veuillez remplir tous les champs et fournir un justificatif.', 'error');
            return;
        }
        setIsSubmitting(true);

        const absenceData: Omit<AbsenceInsert, 'id' | 'organization_id' | 'employee_id' | 'attachment_url'> = {
            start_date: startDate,
            end_date: endDate,
            reason: reason,
            justified: isJustified,
            ocr_text: null,
            doctor_name: analysisResult?.doctorName || null,
            extracted_reason: analysisResult?.reason || null,
            extracted_start_date: analysisResult?.startDate || null,
            extracted_end_date: analysisResult?.endDate || null,
            extracted_patient_name: analysisResult?.patientName || null,
            stamp_detected: analysisResult?.stampDetected || null,
        };

        try {
            await onAddAbsence(absenceData, file);
        } finally {
            setIsSubmitting(false);
        }
    };

    return (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex justify-center items-center z-50 p-4">
            <div className="bg-white rounded-xl shadow-2xl w-full max-w-lg p-8 m-4 max-h-[90vh] overflow-y-auto">
                <div className="flex justify-between items-center mb-6">
                    <div>
                        <h2 className="text-2xl font-bold text-gray-800 uppercase tracking-wide">Analyser un certificat</h2>
                        <p className="text-sm text-gray-500">Pour : <span className="font-semibold">{employee.name}</span></p>
                    </div>
                    <button onClick={onClose} className="text-gray-400 hover:text-gray-600">
                        <Icon.Close className="h-6 w-6" />
                    </button>
                </div>
                <form onSubmit={handleSubmit} className="space-y-6">
                    <div>
                        <label className="block text-sm font-medium text-gray-700 mb-1">Justificatif (Certificat médical)</label>
                        <div className="mt-1 flex justify-center px-6 pt-5 pb-6 border-2 border-gray-300 border-dashed rounded-md">
                            <div className="space-y-1 text-center">
                                <Icon.Upload className="mx-auto h-12 w-12 text-gray-400" />
                                <div className="flex text-sm text-gray-600">
                                    <label htmlFor="file-upload" className="relative cursor-pointer bg-white rounded-md font-medium text-[--org-color-main] hover:text-[--org-color-dark] focus-within:outline-none">
                                        <span>Téléversez un fichier</span>
                                        <input id="file-upload" name="file-upload" type="file" className="sr-only" onChange={(e) => handleFileSelected(e.target.files?.[0] || null)} disabled={isAnalyzing}/>
                                    </label>
                                </div>
                                {file ? (
                                    <p className="text-sm text-green-600 font-semibold pt-2">{file.name}</p>
                                ) : (
                                    <p className="text-xs text-gray-500">PDF, PNG, JPG</p>
                                )}
                            </div>
                        </div>
                    </div>

                    {isAnalyzing && (
                        <div className="text-center p-4 bg-blue-50 rounded-lg flex items-center justify-center">
                            <Spinner size="sm" />
                            <p className="ml-3 font-semibold text-blue-800">Analyse du certificat par l'IA en cours...</p>
                        </div>
                    )}
                    
                    {analysisResult && (
                         <div className="p-4 bg-gray-50 rounded-lg border space-y-4">
                            <div>
                                <h3 className="text-md font-semibold text-gray-700 mb-2">Résultats de l'analyse IA</h3>
                                <div className="grid grid-cols-1 sm:grid-cols-2 gap-x-4 gap-y-2 text-sm">
                                    <p>Patient: <span className="font-medium text-gray-900">{analysisResult.patientName || 'N/A'}</span></p>
                                    <p>Médecin: <span className="font-medium text-gray-900">{analysisResult.doctorName || 'N/A'}</span></p>
                                    <p>Cachet détecté: <span className={`font-medium ${analysisResult.stampDetected ? 'text-green-600' : 'text-red-600'}`}>{analysisResult.stampDetected ? 'Oui' : 'Non'}</span></p>
                                </div>
                            </div>
                            <div className="border-t pt-3">
                                <h4 className="text-md font-semibold text-gray-700 mb-2">Vérification de Conformité</h4>
                                <ul className="space-y-1 text-sm">
                                    <li className="flex items-center">
                                        {conformity.patientNameMatch === null ? <Icon.QuestionMarkCircle className="h-5 w-5 mr-2 text-gray-400" /> : (conformity.patientNameMatch ? <Icon.CheckCircle className="h-5 w-5 mr-2 text-green-500" /> : <Icon.Warning className="h-5 w-5 mr-2 text-red-500" />)}
                                        <span className={conformity.patientNameMatch === false ? 'text-red-600 font-semibold' : 'text-gray-700'}>
                                            Nom du patient correspond à l'employé
                                        </span>
                                    </li>
                                    <li className="flex items-center">
                                        {conformity.dateConsistent === null ? <Icon.QuestionMarkCircle className="h-5 w-5 mr-2 text-gray-400" /> : (conformity.dateConsistent ? <Icon.CheckCircle className="h-5 w-5 mr-2 text-green-500" /> : <Icon.Warning className="h-5 w-5 mr-2 text-red-500" />)}
                                        <span className={conformity.dateConsistent === false ? 'text-red-600 font-semibold' : 'text-gray-700'}>
                                            Cohérence des dates (début ≤ fin)
                                        </span>
                                    </li>
                                </ul>
                            </div>
                        </div>
                    )}

                    <fieldset disabled={isAnalyzing || isSubmitting}>
                        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                             <div>
                                <label htmlFor="start-date" className="block text-sm font-medium text-gray-700 mb-1">Date de début</label>
                                <input type="date" id="start-date" value={startDate} onChange={e => setStartDate(e.target.value)} required className="w-full p-2 border border-gray-300 rounded-lg focus:ring-[--org-color-main] focus:border-[--org-color-main]" />
                            </div>
                            <div>
                                <label htmlFor="end-date" className="block text-sm font-medium text-gray-700 mb-1">Date de fin</label>
                                <input type="date" id="end-date" value={endDate} onChange={e => setEndDate(e.target.value)} required className="w-full p-2 border border-gray-300 rounded-lg focus:ring-[--org-color-main] focus:border-[--org-color-main]" />
                            </div>
                        </div>
                        <div>
                            <label htmlFor="reason" className="block text-sm font-medium text-gray-700 mb-1">Motif de l'absence</label>
                            <input type="text" id="reason" value={reason} onChange={e => setReason(e.target.value)} required className="w-full p-2 border border-gray-300 rounded-lg focus:ring-[--org-color-main] focus:border-[--org-color-main]" placeholder="Ex: Maladie" />
                        </div>
                        <div className="flex items-center">
                            <input id="justified" type="checkbox" checked={isJustified} onChange={e => setIsJustified(e.target.checked)} className="h-4 w-4 text-[--org-color-main] focus:ring-[--org-color-main] border-gray-300 rounded" />
                            <label htmlFor="justified" className="ml-2 block text-sm text-gray-900">Absence justifiée</label>
                        </div>
                    </fieldset>
                    
                    <div className="mt-8 flex justify-end space-x-4">
                        <button type="button" onClick={onClose} className="py-2 px-4 bg-gray-200 text-gray-800 font-semibold rounded-lg hover:bg-gray-300">Annuler</button>
                        <button type="submit" disabled={isSubmitting || isAnalyzing || !file} className="py-2 px-6 bg-[--org-color-main] text-white font-semibold rounded-lg shadow-md hover:bg-[--org-color-dark] disabled:bg-opacity-60 disabled:cursor-not-allowed flex items-center">
                            {isSubmitting ? <><Spinner size="sm" color="white"/> <span className="ml-2">Sauvegarde...</span></> : 'Enregistrer l\'absence'}
                        </button>
                    </div>
                </form>
            </div>
        </div>
    );
};