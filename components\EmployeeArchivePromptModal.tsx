import React, { useState } from 'react';
import { Employee } from '../types';
import { Icon } from './shared/Icon';
import { Spinner } from './shared/Spinner';

interface EmployeeArchivePromptModalProps {
  employees: Employee[];
  onClose: () => void;
  onArchive: (employeeId: string) => Promise<void>;
}

export const EmployeeArchivePromptModal: React.FC<EmployeeArchivePromptModalProps> = ({ employees, onClose, onArchive }) => {
  const [isArchiving, setIsArchiving] = useState(false);

  const handleArchiveAll = async () => {
    setIsArchiving(true);
    try {
      // Archive employees one by one
      for (const employee of employees) {
        await onArchive(employee.id);
      }
      onClose();
    } catch (error) {
      console.error("Failed to archive employees", error);
      // You might want to show a notification here
    } finally {
      setIsArchiving(false);
    }
  };

  return (
    <div className="fixed inset-0 bg-black bg-opacity-60 flex justify-center items-center z-50 p-4">
      <div className="bg-white rounded-xl shadow-2xl w-full max-w-lg p-8 m-4 text-center modal-content-animate">
        <div className="mx-auto flex items-center justify-center h-16 w-16 rounded-full bg-yellow-100 mb-5">
          <Icon.ArchiveBox className="h-8 w-8 text-yellow-600" />
        </div>
        
        <h2 className="text-2xl font-bold text-gray-800">
          Archivage Suggéré
        </h2>
        <p className="text-gray-600 mt-2">
          Le système suggère d'archiver {employees.length} employé(s).
        </p>

        <div className="mt-4 text-left bg-gray-50 p-3 rounded-lg max-h-40 overflow-y-auto border">
            <ul className="divide-y divide-gray-200">
                {employees.map(emp => (
                    <li key={emp.id} className="py-2">
                        <p className="font-semibold text-gray-800">{emp.name}</p>
                    </li>
                ))}
            </ul>
        </div>

        <p className="text-xs text-gray-500 mt-2">
          L'archivage les retirera des listes actives mais conservera leurs données.
        </p>

        <div className="mt-8 flex flex-col sm:flex-row justify-center gap-4">
          <button 
            onClick={handleArchiveAll}
            disabled={isArchiving}
            className="w-full flex items-center justify-center gap-2 px-4 py-3 text-sm font-semibold text-white bg-[--lica-blue] rounded-lg hover:bg-[--lica-blue-dark] transition-colors disabled:bg-opacity-60"
          >
            {isArchiving ? <> <Spinner size="sm" color="white"/> <span>Archivage en cours...</span> </> : 'Archiver les employés'}
          </button>
          <button 
            type="button" 
            onClick={onClose} 
            className="w-full sm:w-auto py-2 px-4 bg-gray-200 text-gray-800 font-semibold rounded-lg hover:bg-gray-300"
          >
            Ignorer pour le moment
          </button>
        </div>
      </div>
    </div>
  );
};