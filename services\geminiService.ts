import { GoogleGenAI, GenerateContentResponse, Type } from "@google/genai";
import {
  PayrollAnalysis,
  ContentComplianceAnalysis,
  DocumentCategory,
  Anomaly,
  Employee,
  Absence,
  CandidateAnalysisReport
} from '../types';

// Simple configuration
const GEMINI_API_KEY = "AIzaSyDPn7kvZ4BjgoXGxkY_QC1HS6k2i5BTxzk";
const GEMINI_MODEL = "gemini-2.5-flash";

// Initialize Gemini AI
const ai = new GoogleGenAI({ apiKey: GEMINI_API_KEY });

// Simple delay function to avoid rate limits
const delay = (ms: number) => new Promise(resolve => setTimeout(resolve, ms));

// Request counter to track usage
let requestCount = 0;
let lastResetTime = Date.now();

// Simple rate limiting - reset counter every minute
const checkRateLimit = async () => {
  const now = Date.now();
  if (now - lastResetTime > 60000) { // Reset every minute
    requestCount = 0;
    lastResetTime = now;
  }

  if (requestCount >= 8) { // Conservative limit
    console.log('Rate limit reached, waiting 60 seconds...');
    await delay(60000);
    requestCount = 0;
    lastResetTime = Date.now();
  }

  requestCount++;
};

// Utility to convert file to base64
const fileToGenerativePart = async (file: File) => {
  const base64EncodedDataPromise = new Promise<string>((resolve) => {
    const reader = new FileReader();
    reader.onloadend = () => resolve((reader.result as string).split(',')[1]);
    reader.readAsDataURL(file);
  });

  return {
    inlineData: {
      data: await base64EncodedDataPromise,
      mimeType: file.type
    },
  };
};

// Simple retry function
const retryRequest = async <T>(operation: () => Promise<T>, maxRetries = 2): Promise<T> => {
  for (let attempt = 0; attempt <= maxRetries; attempt++) {
    try {
      await checkRateLimit();
      return await operation();
    } catch (error: any) {
      if (error?.error?.code === 429 && attempt < maxRetries) {
        console.log(`Rate limit hit, waiting before retry ${attempt + 1}/${maxRetries}`);
        await delay(30000); // Wait 30 seconds
        continue;
      }
      throw error;
    }
  }
  throw new Error('Max retries exceeded');
};

// Document category detection
export const getDocumentCategory = async (file: File): Promise<DocumentCategory> => {
  try {
    const filePart = await fileToGenerativePart(file);
    const prompt = `Analysez le document et identifiez sa catégorie. Répondez avec un JSON: {"category": "Contrat"} ou "reglement_interne" ou "Diplome" ou "certificat_de_travail".`;

    const result = await retryRequest(async () => {
      const response = await ai.models.generateContent({
        model: GEMINI_MODEL,
        contents: { parts: [filePart, { text: prompt }] },
        config: {
          responseMimeType: "application/json",
          responseSchema: {
            type: Type.OBJECT,
            properties: {
              category: {
                type: Type.STRING,
                enum: ['Contrat', 'reglement_interne', 'Diplome', 'certificat_de_travail']
              }
            },
            required: ["category"]
          }
        }
      });

      const jsonText = response.text.trim();
      const parsed = JSON.parse(jsonText) as { category: DocumentCategory };
      return parsed.category;
    });

    return result;
  } catch (error) {
    console.error("Error identifying document category:", error);
    return 'Contrat'; // Safe fallback
  }
};

// Medical certificate analysis interface
export interface MedicalCertificateAnalysis {
  patientName: string | null;
  doctorName: string | null;
  reason: string;
  startDate: string;
  endDate: string;
  stampDetected: boolean;
}

// Medical certificate analysis
export const analyzeMedicalCertificate = async (file: File): Promise<MedicalCertificateAnalysis> => {
  const filePart = await fileToGenerativePart(file);
  const prompt = `Analysez ce certificat médical et extrayez les informations au format JSON:
  {
    "patientName": "nom du patient ou null",
    "doctorName": "nom du médecin ou null",
    "reason": "raison de l'arrêt",
    "startDate": "YYYY-MM-DD",
    "endDate": "YYYY-MM-DD",
    "stampDetected": true/false
  }`;

  const responseSchema = {
    type: Type.OBJECT,
    properties: {
      patientName: { type: Type.STRING, nullable: true },
      doctorName: { type: Type.STRING, nullable: true },
      reason: { type: Type.STRING },
      startDate: { type: Type.STRING },
      endDate: { type: Type.STRING },
      stampDetected: { type: Type.BOOLEAN },
    },
    required: ["patientName", "doctorName", "reason", "startDate", "endDate", "stampDetected"]
  };

  try {
    return await retryRequest(async () => {
      const response = await ai.models.generateContent({
        model: GEMINI_MODEL,
        contents: { parts: [filePart, { text: prompt }] },
        config: {
          responseMimeType: "application/json",
          responseSchema
        }
      });

      const jsonText = response.text.trim();
      return JSON.parse(jsonText) as MedicalCertificateAnalysis;
    });
  } catch (error) {
    console.error("Error analyzing medical certificate:", error);
    throw new Error("L'analyse du certificat médical a échoué.");
  }
};

// CV Analysis
export const analyzeCandidateCV = async (file: File): Promise<CandidateAnalysisReport> => {
  const filePart = await fileToGenerativePart(file);
  const prompt = `Analysez ce CV et retournez un rapport JSON structuré avec:
  - status: "Conforme" ou "Non Conforme"
  - summary: résumé en 2-3 phrases
  - extractedData: {name, email, phone, education[], experience[], skills[], languages[]}
  - findings: [{type, description}] pour les anomalies détectées`;

  const responseSchema = {
    type: Type.OBJECT,
    properties: {
      status: { type: Type.STRING, enum: ['Conforme', 'Non Conforme'] },
      summary: { type: Type.STRING },
      extractedData: {
        type: Type.OBJECT,
        properties: {
          name: { type: Type.STRING, nullable: true },
          email: { type: Type.STRING, nullable: true },
          phone: { type: Type.STRING, nullable: true },
          education: {
            type: Type.ARRAY,
            items: {
              type: Type.OBJECT,
              properties: {
                degree: { type: Type.STRING },
                institution: { type: Type.STRING },
                dates: { type: Type.STRING }
              },
              required: ['degree', 'institution', 'dates']
            }
          },
          experience: {
            type: Type.ARRAY,
            items: {
              type: Type.OBJECT,
              properties: {
                title: { type: Type.STRING },
                company: { type: Type.STRING },
                dates: { type: Type.STRING },
                description: { type: Type.STRING }
              },
              required: ['title', 'company', 'dates', 'description']
            }
          },
          skills: { type: Type.ARRAY, items: { type: Type.STRING } },
          languages: { type: Type.ARRAY, items: { type: Type.STRING } }
        },
        required: ['name', 'email', 'phone', 'education', 'experience', 'skills', 'languages']
      },
      findings: {
        type: Type.ARRAY,
        items: {
          type: Type.OBJECT,
          properties: {
            type: { type: Type.STRING, enum: ['inconsistency', 'missing_info', 'compliance_issue', 'positive_point'] },
            description: { type: Type.STRING }
          },
          required: ['type', 'description']
        }
      }
    },
    required: ['status', 'summary', 'extractedData', 'findings']
  };

  try {
    return await retryRequest(async () => {
      const response = await ai.models.generateContent({
        model: GEMINI_MODEL,
        contents: { parts: [filePart, { text: prompt }] },
        config: {
          responseMimeType: "application/json",
          responseSchema
        }
      });

      const jsonText = response.text.trim();
      return JSON.parse(jsonText) as CandidateAnalysisReport;
    });
  } catch (error) {
    console.error("Error analyzing CV:", error);
    throw new Error("L'analyse du CV a échoué.");
  }
};

// Absence anomaly analysis - SIMPLIFIED to avoid loops
export const analyzeAbsencesForAnomalies = async (absences: Absence[], employees: Employee[]): Promise<Anomaly[]> => {
  if (absences.length === 0) return [];

  try {
    const employeeMap = new Map(employees.map(e => [e.id, e.name]));
    const absenceData = absences.map(a => ({
      employeeName: employeeMap.get(a.employeeId) || `ID:${a.employeeId}`,
      startDate: a.startDate,
      endDate: a.endDate,
      reason: a.reason,
      justified: a.justified
    }));

    const prompt = `Analysez ces absences et détectez les anomalies. Retournez un JSON:
    {"anomalies": [{"employeeName": "nom", "description": "problème détecté", "severity": "Faible|Moyen|Élevé"}]}

    Données: ${JSON.stringify(absenceData.slice(0, 10))}`;

    const responseSchema = {
      type: Type.OBJECT,
      properties: {
        anomalies: {
          type: Type.ARRAY,
          items: {
            type: Type.OBJECT,
            properties: {
              employeeName: { type: Type.STRING },
              description: { type: Type.STRING },
              severity: { type: Type.STRING, enum: ['Faible', 'Moyen', 'Élevé'] },
            },
            required: ["employeeName", "description", "severity"]
          }
        }
      },
      required: ["anomalies"]
    };

    const result = await retryRequest(async () => {
      const response = await ai.models.generateContent({
        model: GEMINI_MODEL,
        contents: { parts: [{ text: prompt }] },
        config: {
          responseMimeType: "application/json",
          responseSchema
        }
      });

      const jsonText = response.text.trim();
      const parsed = JSON.parse(jsonText) as { anomalies: Anomaly[] };
      return parsed.anomalies || [];
    });

    return result;
  } catch (error: any) {
    console.error("Error analyzing absences:", error);
    // Return a simple error message instead of throwing
    return [{
      employeeName: "",
      description: "Analyse des anomalies temporairement indisponible",
      severity: "Faible"
    }];
  }
};

// Document content analysis
export const analyzeDocumentContent = async (file: File, category: DocumentCategory): Promise<ContentComplianceAnalysis> => {
  const filePart = await fileToGenerativePart(file);
  const prompt = `Analysez ce document ${category} pour la conformité. Retournez un JSON:
  {
    "isCompliant": true/false,
    "summary": "résumé de l'analyse",
    "findings": ["éléments conformes trouvés"],
    "issues": ["problèmes détectés"]
  }`;

  const responseSchema = {
    type: Type.OBJECT,
    properties: {
      isCompliant: { type: Type.BOOLEAN },
      summary: { type: Type.STRING },
      findings: { type: Type.ARRAY, items: { type: Type.STRING } },
      issues: { type: Type.ARRAY, items: { type: Type.STRING } },
    },
    required: ["isCompliant", "summary", "findings", "issues"]
  };

  try {
    return await retryRequest(async () => {
      const response = await ai.models.generateContent({
        model: GEMINI_MODEL,
        contents: { parts: [filePart, { text: prompt }] },
        config: {
          responseMimeType: "application/json",
          responseSchema
        }
      });

      const jsonText = response.text.trim();
      return JSON.parse(jsonText) as ContentComplianceAnalysis;
    });
  } catch (error) {
    console.error(`Error analyzing document content:`, error);
    throw new Error(`L'analyse du document a échoué.`);
  }
};

// Payroll analysis
export const analyzePayrollData = async (file: File): Promise<PayrollAnalysis> => {
  const filePart = await fileToGenerativePart(file);
  const prompt = `Analysez ce document de paie pour les heures supplémentaires. Retournez un JSON:
  {
    "totalOvertimeHours": nombre_total,
    "totalOvertimeCost": coût_total,
    "topOvertimeEmployees": [{"employeeName": "nom", "overtimeHours": heures, "overtimeCost": coût}],
    "potentialAnomalies": [{"employeeName": "nom", "description": "anomalie", "riskLevel": "Faible|Moyen|Élevé"}],
    "summary": "résumé de l'analyse"
  }`;

  const responseSchema = {
    type: Type.OBJECT,
    properties: {
      totalOvertimeHours: { type: Type.NUMBER },
      totalOvertimeCost: { type: Type.NUMBER },
      topOvertimeEmployees: {
        type: Type.ARRAY,
        items: {
          type: Type.OBJECT,
          properties: {
            employeeName: { type: Type.STRING },
            overtimeHours: { type: Type.NUMBER },
            overtimeCost: { type: Type.NUMBER },
          },
          required: ["employeeName", "overtimeHours", "overtimeCost"]
        }
      },
      potentialAnomalies: {
        type: Type.ARRAY,
        items: {
          type: Type.OBJECT,
          properties: {
            employeeName: { type: Type.STRING },
            description: { type: Type.STRING },
            riskLevel: { type: Type.STRING, enum: ['Faible', 'Moyen', 'Élevé'] },
          },
          required: ["employeeName", "description", "riskLevel"]
        }
      },
      summary: { type: Type.STRING }
    },
    required: ["totalOvertimeHours", "totalOvertimeCost", "topOvertimeEmployees", "potentialAnomalies", "summary"]
  };

  try {
    return await retryRequest(async () => {
      const response = await ai.models.generateContent({
        model: GEMINI_MODEL,
        contents: { parts: [filePart, { text: prompt }] },
        config: {
          responseMimeType: "application/json",
          responseSchema
        }
      });

      const jsonText = response.text.trim();
      return JSON.parse(jsonText) as PayrollAnalysis;
    });
  } catch (error) {
    console.error("Error analyzing payroll:", error);
    throw new Error("L'analyse de la paie a échoué.");
  }
};