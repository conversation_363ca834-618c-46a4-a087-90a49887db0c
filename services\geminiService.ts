// MOCK SERVICE - Google Gemini services temporarily disabled
import {
  PayrollAnalysis,
  ContentComplianceAnalysis,
  DocumentCategory,
  Anomaly,
  Employee,
  Absence,
  CandidateAnalysisReport
} from '../types';

// Mock delay to simulate processing
const mockDelay = (ms: number = 1000) => new Promise(resolve => setTimeout(resolve, ms));

// Document category detection - MOCK
export const getDocumentCategory = async (file: File): Promise<DocumentCategory> => {
  await mockDelay(500);
  console.log('Mock: Document category detection for:', file.name);

  // Simple mock logic based on filename
  const fileName = file.name.toLowerCase();
  if (fileName.includes('diplom') || fileName.includes('degree')) return 'Diplome';
  if (fileName.includes('contrat') || fileName.includes('contract')) return 'Contrat';
  if (fileName.includes('reglement') || fileName.includes('rules')) return 'reglement_interne';
  if (fileName.includes('certificat') || fileName.includes('certificate')) return 'certificat_de_travail';

  return 'Contrat'; // Default
};

// Medical certificate analysis interface
export interface MedicalCertificateAnalysis {
  patientName: string | null;
  doctorName: string | null;
  reason: string;
  startDate: string;
  endDate: string;
  stampDetected: boolean;
}

// Medical certificate analysis - MOCK
export const analyzeMedicalCertificate = async (file: File): Promise<MedicalCertificateAnalysis> => {
  await mockDelay(1000);
  console.log('Mock: Medical certificate analysis for:', file.name);

  return {
    patientName: "Patient Exemple",
    doctorName: "Dr. Exemple",
    reason: "Repos médical",
    startDate: "2024-01-15",
    endDate: "2024-01-20",
    stampDetected: true
  };
};

// CV Analysis - MOCK
export const analyzeCandidateCV = async (file: File): Promise<CandidateAnalysisReport> => {
  await mockDelay(1500);
  console.log('Mock: CV analysis for:', file.name);

  return {
    status: 'Conforme',
    summary: 'CV bien structuré avec des informations complètes et cohérentes.',
    extractedData: {
      name: "Candidat Exemple",
      email: "<EMAIL>",
      phone: "+33 1 23 45 67 89",
      education: [
        {
          degree: "Master en Informatique",
          institution: "Université Exemple",
          dates: "2020-2022"
        }
      ],
      experience: [
        {
          title: "Développeur",
          company: "Entreprise Exemple",
          dates: "2022-2024",
          description: "Développement d'applications web"
        }
      ],
      skills: ["JavaScript", "React", "Node.js"],
      languages: ["Français (natif)", "Anglais (courant)"]
    },
    findings: [
      {
        type: 'positive_point',
        description: 'CV bien organisé et complet'
      }
    ]
  };
};

// Absence anomaly analysis - MOCK
export const analyzeAbsencesForAnomalies = async (absences: Absence[], employees: Employee[]): Promise<Anomaly[]> => {
  await mockDelay(800);
  console.log('Mock: Absence anomaly analysis for', absences.length, 'absences');

  // Simple mock logic - find unjustified absences
  const anomalies: Anomaly[] = [];
  const employeeMap = new Map(employees.map(e => [e.id, e.name]));

  absences.forEach(absence => {
    if (!absence.justified) {
      anomalies.push({
        employeeName: employeeMap.get(absence.employeeId) || `ID:${absence.employeeId}`,
        description: "Absence non justifiée détectée",
        severity: "Élevé"
      });
    }
  });

  // Add some mock patterns if no real anomalies
  if (anomalies.length === 0 && absences.length > 5) {
    anomalies.push({
      employeeName: "Analyse automatique",
      description: "Aucune anomalie majeure détectée dans les absences",
      severity: "Faible"
    });
  }

  return anomalies;
};

// Document content analysis - MOCK
export const analyzeDocumentContent = async (file: File, category: DocumentCategory): Promise<ContentComplianceAnalysis> => {
  await mockDelay(1200);
  console.log('Mock: Document content analysis for:', file.name, 'category:', category);

  return {
    isCompliant: true,
    summary: `Document ${category} analysé - conforme aux standards requis.`,
    findings: [
      "Structure du document appropriée",
      "Informations essentielles présentes",
      "Format respecté"
    ],
    issues: []
  };
};

// Payroll analysis - MOCK
export const analyzePayrollData = async (file: File): Promise<PayrollAnalysis> => {
  await mockDelay(1500);
  console.log('Mock: Payroll analysis for:', file.name);

  return {
    totalOvertimeHours: 45.5,
    totalOvertimeCost: 1250.75,
    topOvertimeEmployees: [
      {
        employeeName: "Employé A",
        overtimeHours: 15.5,
        overtimeCost: 425.50
      },
      {
        employeeName: "Employé B",
        overtimeHours: 12.0,
        overtimeCost: 330.00
      }
    ],
    potentialAnomalies: [
      {
        employeeName: "Employé A",
        description: "Volume d'heures supplémentaires élevé ce mois",
        riskLevel: "Moyen"
      }
    ],
    summary: "Analyse de paie complétée. Volume d'heures supplémentaires dans la normale avec quelques points d'attention."
  };
};