import { GoogleGenAI, GenerateContentResponse, Type } from "@google/genai";
import { HRDocument, PayrollAnalysis, ContentComplianceAnalysis, DocumentCategory, Anomaly, Employee, Absence, CandidateAnalysisReport } from '../types';

const ai = new GoogleGenAI({ apiKey: "AIzaSyDPn7kvZ4BjgoXGxkY_QC1HS6k2i5BTxzk" });

// Utility to convert file to base64
const fileToGenerativePart = async (file: File) => {
  const base64EncodedDataPromise = new Promise<string>((resolve) => {
    const reader = new FileReader();
    reader.onloadend = () => resolve((reader.result as string).split(',')[1]);
    reader.readAsDataURL(file);
  });
  return {
    inlineData: { data: await base64EncodedDataPromise, mimeType: file.type },
  };
};

export const getDocumentCategory = async (file: File): Promise<DocumentCategory> => {
    const filePart = await fileToGenerativePart(file);
    const prompt = `Analysez le document suivant et identifiez sa catégorie. Répondez UNIQUEMENT avec un objet JSON contenant une seule clé "category" avec l'une de ces valeurs exactes : "Contrat", "reglement_interne", "Diplome", "certificat_de_travail". Si vous ne parvenez pas à déterminer la catégorie, utilisez "Contrat" par défaut.`;

    try {
        const response: GenerateContentResponse = await ai.models.generateContent({
            model: "gemini-2.5-flash",
            contents: { parts: [filePart, { text: prompt }] },
            config: {
                responseMimeType: "application/json",
                responseSchema: {
                    type: Type.OBJECT,
                    properties: {
                        category: { type: Type.STRING, enum: ['Contrat', 'reglement_interne', 'Diplome', 'certificat_de_travail'] }
                    },
                    required: ["category"]
                }
            }
        });
        const jsonText = response.text.trim();
        const result = JSON.parse(jsonText) as { category: DocumentCategory };
        return result.category;
    } catch (error) {
        console.error("Erreur d'identification de la catégorie du document:", error);
        // Utiliser une catégorie par défaut en cas d'échec pour ne pas bloquer le téléversement.
        return 'Contrat'; 
    }
};

export const analyzeCandidateCV = async (file: File): Promise<CandidateAnalysisReport> => {
    const filePart = await fileToGenerativePart(file);
    const prompt = `
    En tant qu'auditeur RH expert, spécialisé dans le recrutement en France, analysez le CV suivant. Votre mission est d'extraire les informations, de vérifier la cohérence, de détecter les problèmes de conformité, et de produire un rapport structuré.

    **1. Extraction des Données :**
    Extrayez les informations suivantes de manière précise. Si une information est absente, retournez 
null
 ou un tableau vide.
    - 
name
: Nom complet du candidat.
    - 
email
: Adresse email.
    - 
phone
: Numéro de téléphone.
    - 
education
: Un tableau d'objets contenant 

degree
, 

institution
, et 

dates
.
    - 
experience
: Un tableau d'objets contenant 

title
 (poste), 

company
, 

dates
, et 

description
 (description concise des missions).
    - 
skills
: Un tableau des compétences techniques ou soft skills listées.
    - 
languages
: Un tableau des langues parlées et leur niveau (si précisé).

    **2. Analyse et Validation :**
    Analysez les données extraites pour identifier des points d'attention. Pour chaque point, créez un objet dans le tableau 
findings
.
    - 
inconsistency
: Détectez les incohérences. Exemples: dates d'expérience qui se chevauchent, dates de diplôme illogiques.
    - 
missing_info
: Identifiez les informations cruciales manquantes. Exemples: pas de dates pour une expérience, pas d'informations de contact.
    - 
compliance_issue
: Signalez toute information potentiellement discriminatoire selon la loi française (photo, âge, état civil, nationalité non-UE sans mention de titre de séjour). Soyez prudent et ne signalez que les cas évidents.
    - 
positive_point
: (Optionnel) Mentionnez un point particulièrement positif (ex: certification très recherchée).

    **3. Synthèse et Statut :**
    - 
summary
: Rédigez un résumé exécutif de 2-3 phrases sur le profil et la qualité du CV.
    - 
status
: Déterminez un statut global.
        - "Conforme": Si le CV est clair, cohérent, et sans problème de conformité majeur.
        - "Non Conforme": Si des incohérences majeures, des informations critiques manquantes, ou des problèmes de conformité sont détectés.

    **Format de Sortie :**
    Répondez IMPÉRATIVEMENT et UNIQUEMENT avec un objet JSON qui respecte le schéma fourni.
    `;

    try {
        const response: GenerateContentResponse = await ai.models.generateContent({
            model: "gemini-2.5-flash",
            contents: { parts: [filePart, { text: prompt }] },
            config: {
                responseMimeType: "application/json",
                responseSchema: {
                    type: Type.OBJECT,
                    properties: {
                        status: { type: Type.STRING, enum: ['Conforme', 'Non Conforme'] },
                        summary: { type: Type.STRING },
                        extractedData: {
                            type: Type.OBJECT,
                            properties: {
                                name: { type: Type.STRING, nullable: true },
                                email: { type: Type.STRING, nullable: true },
                                phone: { type: Type.STRING, nullable: true },
                                education: {
                                    type: Type.ARRAY,
                                    items: {
                                        type: Type.OBJECT,
                                        properties: {
                                            degree: { type: Type.STRING },
                                            institution: { type: Type.STRING },
                                            dates: { type: Type.STRING }
                                        },
                                        required: ['degree', 'institution', 'dates']
                                    }
                                },
                                experience: {
                                    type: Type.ARRAY,
                                    items: {
                                        type: Type.OBJECT,
                                        properties: {
                                            title: { type: Type.STRING },
                                            company: { type: Type.STRING },
                                            dates: { type: Type.STRING },
                                            description: { type: Type.STRING }
                                        },
                                        required: ['title', 'company', 'dates', 'description']
                                    }
                                },
                                skills: { type: Type.ARRAY, items: { type: Type.STRING } },
                                languages: { type: Type.ARRAY, items: { type: Type.STRING } }
                            },
                            required: ['name', 'email', 'phone', 'education', 'experience', 'skills', 'languages']
                        },
                        findings: {
                            type: Type.ARRAY,
                            items: {
                                type: Type.OBJECT,
                                properties: {
                                    type: { type: Type.STRING, enum: ['inconsistency', 'missing_info', 'compliance_issue', 'positive_point'] },
                                    description: { type: Type.STRING }
                                },
                                required: ['type', 'description']
                            }
                        }
                    },
                    required: ['status', 'summary', 'extractedData', 'findings']
                }
            }
        });
        const jsonText = response.text.trim();
        return JSON.parse(jsonText) as CandidateAnalysisReport;
    } catch (error) {
        console.error("Error analyzing CV:", error);
        throw new Error("L'analyse IA du CV a échoué. Veuillez vérifier la clarté et le format du document.");
    }
};

export interface MedicalCertificateAnalysis {
  patientName: string | null;
  doctorName: string | null;
  reason: string;
  startDate: string; // "YYYY-MM-DD"
  endDate: string; // "YYYY-MM-DD"
  stampDetected: boolean;
}

export const analyzeAbsencesForAnomalies = async (absences: Absence[], employees: Employee[]): Promise<Anomaly[]> => {
    if (absences.length === 0) return [];

    const employeeMap = new Map(employees.map(e => [e.id, e.name]));
    const absenceData = absences.map(a => ({
        employeeName: employeeMap.get(a.employeeId) || `ID:${a.employeeId}`,
        startDate: a.startDate,
        endDate: a.endDate,
        reason: a.reason,
        justified: a.justified
    }));

    const prompt = `
    En tant qu'auditeur RH expert, analysez cette liste d'absences. Votre mission est de détecter des schémas suspects ou des anomalies qui pourraient indiquer un abus ou des problèmes de management.

    Règles d'analyse :
    1.  **Absences Répétées et Courtes :** Un employé avec 3 absences ou plus de 1-2 jours sur la période fournie est une anomalie de sévérité 'Moyen'.
    2.  **Schéma "Long Weekend" :** Des absences qui commencent un Lundi ou terminent un Vendredi de manière répétée (2 fois ou plus) pour un même employé sont une anomalie de sévérité 'Moyen'.
    3.  **Absences non justifiées :** Toute absence marquée comme "justified: false" est une anomalie de sévérité 'Élevé'.
    4.  **Absences groupées :** Si plusieurs employés sont absents en même temps avec des motifs similaires, cela peut indiquer un problème plus large (sévérité 'Faible' à 'Moyen').

    Tâche :
    Analysez les données d'absence JSON fournies. Retournez IMPÉRATIVEMENT et UNIQUEMENT un objet JSON contenant une clé "anomalies". Cette clé doit contenir un tableau des anomalies détectées. Pour chaque anomalie, fournissez le nom de l'employé, une description claire et précise du problème, et un niveau de sévérité ('Faible', 'Moyen', ou 'Élevé'). Si aucune anomalie n'est trouvée, retournez un tableau vide.
    `;
    
    let lastError;
    for (let attempt = 0; attempt < 2; attempt++) {
        try {
            const response: GenerateContentResponse = await ai.models.generateContent({
                model: "gemini-2.5-flash",
                contents: { parts: [{ text: prompt }, { text: `Données d'absences: ${JSON.stringify(absenceData)}` }] },
                config: {
                    responseMimeType: "application/json",
                    responseSchema: {
                       type: Type.OBJECT,
                       properties: {
                           anomalies: {
                               type: Type.ARRAY,
                               description: "Liste des anomalies détectées.",
                               items: {
                                   type: Type.OBJECT,
                                   properties: {
                                       employeeName: { type: Type.STRING },
                                       description: { type: Type.STRING },
                                       severity: { type: Type.STRING, enum: ['Faible', 'Moyen', 'Élevé'] },
                                   },
                                   required: ["employeeName", "description", "severity"]
                               }
                           }
                       },
                       required: ["anomalies"]
                    }
                }
            });
            const jsonText = response.text.trim();
            const result = JSON.parse(jsonText) as { anomalies: Anomaly[] };
            return result.anomalies || [];
        } catch (error: any) {
            lastError = error;
            // Gestion quota Gemini
            if (error?.error?.code === 429 && error?.error?.details) {
                const retryInfo = error.error.details.find((d: any) => d['@type'] === 'type.googleapis.com/google.rpc.RetryInfo');
                const retryDelay = retryInfo?.retryDelay ? parseInt(retryInfo.retryDelay.replace('s', '')) : 20;
                if (attempt === 0) {
                    // Affiche dans la console et attend avant de réessayer
                    console.warn(`Quota Gemini dépassé, nouvelle tentative dans ${retryDelay}s...`);
                    await new Promise(res => setTimeout(res, retryDelay * 1000));
                    continue;
                }
            }
            break;
        }
    }
    // Si quota dépassé, retourne une erreur spécifique pour affichage côté dashboard
    if (lastError?.error?.code === 429) {
        return [{
            employeeName: "",
            description: "Quota Gemini API dépassé. Veuillez patienter ou vérifier votre abonnement.",
            severity: "Élevé"
        }];
    }
    console.error("Error analyzing absences for anomalies:", lastError);
    throw new Error("L'analyse IA des anomalies d'absence a échoué.");
};

export const analyzeMedicalCertificate = async (file: File): Promise<MedicalCertificateAnalysis> => {
    const filePart = await fileToGenerativePart(file);

    const prompt = `
    En tant qu'auditeur RH expert, analysez ce certificat médical. Votre mission est d'extraire les informations clés avec une grande précision et de vérifier les marqueurs d'authenticité.

    **Instructions :**
    1.  **Extraction des Données :**
        -   **patientName :** Identifiez le nom complet du patient. S'il n'est pas clairement identifiable, retournez 
null
.
        -   **doctorName :** Identifiez le nom complet du médecin signataire. S'il n'est pas clairement identifiable, retournez 
null
.
        -   **reason :** Extrayez la raison ou le diagnostic de l'arrêt de travail. Soyez concis. S'il n'y en a pas, indiquez "Non spécifié".
        -   **startDate :** Identifiez la date de début de l'arrêt. Retournez-la IMPÉRATIVEMENT au format AAAA-MM-JJ.
        -   **endDate :** Identifiez la date de fin de l'arrêt. Retournez-la IMPÉRATIVEMENT au format AAAA-MM-JJ.
    2.  **Vérification d'Authenticité :**
        -   **stampDetected :** Déterminez si un cachet (tampon) de médecin ou d'établissement de santé est visible sur le document. Répondez par 
true
 ou 
false
. Un tampon contient généralement le nom du médecin, son adresse et son numéro.

    **Format de Sortie :**
    Répondez IMPÉRATIVEMENT et UNIQUEMENT avec un objet JSON qui respecte le schéma fourni. Ne retournez aucun texte en dehors de l'objet JSON.
    `;

    try {
        const response: GenerateContentResponse = await ai.models.generateContent({
            model: "gemini-2.5-flash",
            contents: { parts: [filePart, { text: prompt }] },
            config: {
                responseMimeType: "application/json",
                responseSchema: {
                    type: Type.OBJECT,
                    properties: {
                        patientName: { type: Type.STRING, nullable: true, description: "Nom complet du patient." },
                        doctorName: { type: Type.STRING, nullable: true, description: "Nom complet du médecin." },
                        reason: { type: Type.STRING, description: "Raison de l'arrêt de travail." },
                        startDate: { type: Type.STRING, description: "Date de début au format AAAA-MM-JJ." },
                        endDate: { type: Type.STRING, description: "Date de fin au format AAAA-MM-JJ." },
                        stampDetected: { type: Type.BOOLEAN, description: "Un cachet a-t-il été détecté ?" },
                    },
                    required: ["patientName", "doctorName", "reason", "startDate", "endDate", "stampDetected"]
                }
            }
        });
        const jsonText = response.text.trim();
        return JSON.parse(jsonText) as MedicalCertificateAnalysis;
    } catch (error) {
        console.error("Error analyzing medical certificate:", error);
        throw new Error("L'analyse IA du certificat médical a échoué. Vérifiez la clarté de l'image.");
    }
};

export const analyzeDocumentContent = async (file: File, category: DocumentCategory): Promise<ContentComplianceAnalysis> => {
    const filePart = await fileToGenerativePart(file);
    let prompt = '';

    // Define prompts for each category
    switch (category) {
        case 'Diplome':
            prompt = `En tant qu'auditeur expert en vérification de documents académiques, analysez ce diplome. Votre mission est de vérifier son authenticité.
            Règles de vérification : Un diplôme authentique doit contenir clairement: Nom de l'étudiant, nom de l'institution, intitulé du diplôme, date d'obtention, un cachet officiel et au moins une signature autorisée.
            Votre tâche :
            1.  **Analyse d'authenticité** : Comparez le document aux règles. Générez une liste de "findings" pour les éléments présents et une liste de "issues" pour les éléments manquants ou suspects (ex: "Signature manquante", "Cachet non détecté").
            2.  **Décision** : Déterminez si le document est globalement conforme ("isCompliant": true/false). Il est non-conforme si un élément majeur (cachet, signature, nom étudiant/institution) est manquant.
            3.  **Résumé** : Rédigez un résumé concis (1-2 phrases) de vos conclusions.
            Répondez IMPÉRATIVEMENT et UNIQUEMENT avec un objet JSON.`;
            break;
        case 'Contrat':
            prompt = `En tant qu'auditeur RH expert en droit du travail tunisien, analysez ce contrat de travail.
            Règles de conformité : Un contrat de travail valide doit inclure: l'identité des deux parties (employeur, employé), le poste (fonction), le lieu de travail, la date de début, la durée (CDI/CDD), la rémunération (salaire), et les signatures des deux parties.
            Votre tâche :
            1.  **Analyse de conformité** : Vérifiez la présence de chaque élément clé. Listez les éléments conformes dans "findings" et les éléments manquants ou non-conformes dans "issues".
            2.  **Décision** : Déterminez si le contrat est conforme ("isCompliant": true/false). Il est non-conforme si un élément essentiel (parties, salaire, signatures) est manquant.
            3.  **Résumé** : Rédigez un résumé concis de votre analyse.
            Répondez IMPÉRATIVEMENT et UNIQUEMENT avec un objet JSON.`;
            break;
        case 'reglement_interne':
            prompt = `En tant qu'auditeur RH expert en droit du travail tunisien, analysez ce règlement intérieur.
            Règles de conformité : Un règlement intérieur conforme doit contenir des dispositions sur: les règles générales d'organisation du travail, les mesures de santé et sécurité, et les procédures disciplinaires (échelle des sanctions).
            Votre tâche :
            1.  **Analyse de conformité** : Vérifiez la présence de ces trois sections principales. Listez les sections présentes dans "findings" et les sections manquantes dans "issues".
            2.  **Décision** : Déterminez si le règlement est conforme ("isCompliant": true/false) en se basant sur la présence des sections obligatoires.
            3.  **Résumé** : Rédigez un résumé concis de votre analyse.
            Répondez IMPÉRATIVEMENT et UNIQUEMENT avec un objet JSON.`;
            break;
        case 'certificat_de_travail':
            prompt = `En tant qu'auditeur RH expert en droit du travail tunisien, analysez ce certificat de travail.
            Règles de conformité : Un certificat de travail valide doit indiquer: l'identité de l'employeur et de l'employé, la date de début et la date de fin de l'emploi, ainsi que la nature du ou des postes occupés. Il doit être daté et signé par l'employeur.
            Votre tâche :
            1.  **Analyse de conformité** : Vérifiez la présence de chaque élément clé. Listez les éléments conformes dans "findings" et les éléments manquants ou non-conformes dans "issues".
            2.  **Décision** : Déterminez si le certificat est globalement conforme ("isCompliant": true/false). Il est non-conforme si un élément essentiel est manquant.
            3.  **Résumé** : Rédigez un résumé concis de votre analyse.
            Répondez IMPÉRATIVEMENT et UNIQUEMENT avec un objet JSON.`;
            break;
        default:
            throw new Error("Catégorie de document non supportée pour l'analyse.");
    }
    
    try {
        const response: GenerateContentResponse = await ai.models.generateContent({
            model: "gemini-2.5-flash",
            contents: { parts: [filePart, { text: prompt }] },
            config: {
                responseMimeType: "application/json",
                responseSchema: {
                    type: Type.OBJECT,
                    properties: {
                        isCompliant: { type: Type.BOOLEAN, description: "Le document est-il conforme aux règles énoncées ?" },
                        summary: { type: Type.STRING, description: "Résumé concis de l'analyse." },
                        findings: { type: Type.ARRAY, items: { type: Type.STRING }, description: "Liste des points de conformité validés." },
                        issues: { type: Type.ARRAY, items: { type: Type.STRING }, description: "Liste des problèmes ou éléments manquants." },
                    },
                    required: ["isCompliant", "summary", "findings", "issues"]
                }
            }
        });
        const jsonText = response.text.trim();
        return JSON.parse(jsonText) as ContentComplianceAnalysis;
    } catch (error) {
        console.error(`Error analyzing document content for category ${category}:`, error);
        throw new Error(`L'analyse IA du document (${category}) a échoué.`);
    }
};

export const analyzePayrollData = async (file: File): Promise<PayrollAnalysis> => {
    const filePart = await fileToGenerativePart(file);

    const prompt = `
    En tant qu'auditeur de paie expert, spécialisé dans le droit du travail tunisien, analysez ce document de paie. Votre mission est de vérifier la conformité des calculs d'heures supplémentaires.

    **Contexte Légal (Code du Travail Tunisien) :**
    Le calcul des heures supplémentaires DOIT suivre ces règles de majoration :
    - **Régime 48h/semaine :**
      - Heures de jour : **+25%** sur le taux horaire de base.
      - Heures de nuit (21h-6h) : **+50%** sur le taux horaire de base.
    - **Régime 40h/semaine :**
      - De la 41ème à la 48ème heure : **+50%**.
      - Au-delà de la 48ème heure : **+75%**.
    - **Jours de repos hebdomadaire et jours fériés :**
      - Majoration de **+100%** (salaire doublé).

    **Votre Mission :**
    1.  **Extraction Intelligente :**
        - Lisez le document et identifiez chaque employé.
        - Pour chaque employé, extrayez : son nom, son taux horaire de base, et le détail des heures supplémentaires effectuées.
        - Cherchez des colonnes distinctes pour les différents types d'heures (ex: "HS 25%", "HS 50%", "HS Nuit", "HS Férié"). Si le document ne fait pas la distinction, considérez toutes les heures comme des heures de jour standards (+25%).

    2.  **Calculs et Vérification :**
        - Pour chaque employé, recalculez le coût TOTAL de ses heures supplémentaires en appliquant STRICTEMENT les taux de majoration légaux ci-dessus en fonction du type d'heure identifié.
        - **Coût total = SUM(heures_type_1 * taux_horaire * majoration_1) + SUM(heures_type_2 * taux_horaire * majoration_2) + ...**
        - Comparez votre calcul au montant des heures supplémentaires indiqué sur le bulletin de paie (s'il est présent).

    3.  **Analyse d'Anomalies :**
        - **Erreur de Calcul :** Si votre calcul du coût des heures supplémentaires diffère de celui du document, signalez-le comme une anomalie à risque **Élevé**.
        - **Volume excessif :** Un total de plus de 20 heures supplémentaires par mois pour un employé est une anomalie de risque **Moyen**.
        - **Heures pour Cadres :** Les heures supplémentaires pour des postes de direction ("Manager", "Directeur") sont inhabituelles (risque **Faible**).
        - **Concentration :** Si plus de 70% des heures supplémentaires sont effectuées par moins de 20% des employés, signalez-le comme un déséquilibre de charge de travail (risque **Moyen**).

    4.  **Synthèse et Formatage :**
        - Calculez les totaux globaux (coût et volume d'heures) basés sur VOS calculs.
        - Identifiez le top 5 des employés en termes de coût des heures supplémentaires.
        - Rédigez un résumé exécutif de 2-3 phrases sur vos conclusions.
        - Répondez IMPÉRATIVEMENT et UNIQUEMENT avec un objet JSON qui respecte le schéma fourni.

    **Note :** Soyez très rigoureux. La conformité légale est l'objectif principal.
    `;

    try {
        const response: GenerateContentResponse = await ai.models.generateContent({
            model: "gemini-2.5-flash",
            contents: { parts: [filePart, { text: prompt }] },
            config: {
                responseMimeType: "application/json",
                responseSchema: {
                    type: Type.OBJECT,
                    properties: {
                        totalOvertimeHours: { type: Type.NUMBER, description: "Total des heures supplémentaires pour tous les employés." },
                        totalOvertimeCost: { type: Type.NUMBER, description: "Coût total des heures supplémentaires calculé." },
                        topOvertimeEmployees: {
                            type: Type.ARRAY,
                            description: "Top 5 des employés par heures supplémentaires.",
                            items: {
                                type: Type.OBJECT,
                                properties: {
                                    employeeName: { type: Type.STRING },
                                    overtimeHours: { type: Type.NUMBER },
                                    overtimeCost: { type: Type.NUMBER },
                                },
                                required: ["employeeName", "overtimeHours", "overtimeCost"]
                            }
                        },
                        potentialAnomalies: {
                            type: Type.ARRAY,
                            description: "Liste des anomalies détectées.",
                            items: {
                                type: Type.OBJECT,
                                properties: {
                                    employeeName: { type: Type.STRING },
                                    description: { type: Type.STRING },
                                    riskLevel: { type: Type.STRING, enum: ['Faible', 'Moyen', 'Élevé'] },
                                },
                                required: ["employeeName", "description", "riskLevel"]
                            }
                        },
                        summary: { type: Type.STRING, description: "Résumé exécutif de l'analyse." }
                    },
                    required: ["totalOvertimeHours", "totalOvertimeCost", "topOvertimeEmployees", "potentialAnomalies", "summary"]
                }
            }
        });
        const jsonText = response.text.trim();
        return JSON.parse(jsonText) as PayrollAnalysis;
    } catch (error) {
        console.error("Error analyzing payroll file:", error);
        throw new Error("L'analyse du document de paie par l'IA a échoué. Veuillez vérifier la clarté du document.");
    }
};