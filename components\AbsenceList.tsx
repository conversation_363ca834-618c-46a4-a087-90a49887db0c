



import React, { useState } from 'react';
import type { Database } from '../services/supabaseClient';
import { Icon } from './shared/Icon';

type AbsenceRow = Database['public']['Tables']['absences']['Row'];

interface AbsenceListProps {
  absences: AbsenceRow[];
  employeeName?: string; // Optional for validation
}

const AnalysisDetail: React.FC<{ absence: AbsenceRow; employeeName?: string; }> = ({ absence, employeeName }) => {
    const isPatientNameMatching = employeeName && absence.extracted_patient_name ?
      (employeeName.toLowerCase().includes(absence.extracted_patient_name.toLowerCase()) ||
       absence.extracted_patient_name.toLowerCase().includes(employeeName.toLowerCase()))
      : null;

    const detailItem = (label: string, value: string | null | undefined, icon?: React.ReactNode) => (
        <div className="flex items-start text-sm">
            <p className="w-40 text-gray-500 flex-shrink-0">{label}</p>
            <div className="flex-grow text-gray-800 font-medium flex items-center">
                {value || <span className="text-gray-400 italic">Non détecté</span>}
                {icon && <span className="ml-2">{icon}</span>}
            </div>
        </div>
    );

    return (
        <div className="bg-gray-50 p-4 space-y-2 border-l-4 border-[--org-color-main]">
            <h4 className="font-semibold text-gray-800 mb-2">Détails de l'Analyse IA du Justificatif</h4>
            {detailItem("Patient extrait", absence.extracted_patient_name,
                isPatientNameMatching === true ? <Icon.CheckCircle className="h-5 w-5 text-green-500" /> :
                isPatientNameMatching === false ? <Icon.Warning className="h-5 w-5 text-red-500" /> : null
            )}
            {detailItem("Médecin extrait", absence.doctor_name)}
            {detailItem("Date début (IA)", absence.extracted_start_date ? new Date(absence.extracted_start_date).toLocaleDateString('fr-FR') : null)}
            {detailItem("Date fin (IA)", absence.extracted_end_date ? new Date(absence.extracted_end_date).toLocaleDateString('fr-FR') : null)}
            {detailItem("Motif (IA)", absence.extracted_reason)}
            {detailItem("Cachet détecté", absence.stamp_detected === null ? null : (absence.stamp_detected ? 'Oui' : 'Non'),
                absence.stamp_detected === true ? <Icon.CheckCircle className="h-5 w-5 text-green-500"/> :
                absence.stamp_detected === false ? <Icon.Warning className="h-5 w-5 text-yellow-500" /> : null
            )}
        </div>
    );
};


export const AbsenceList: React.FC<AbsenceListProps> = ({ absences, employeeName }) => {
  const [expandedRowId, setExpandedRowId] = useState<string | null>(null);

  if (absences.length === 0) {
    return (
      <div className="text-center py-10 px-6">
        <Icon.Calendar className="h-12 w-12 text-gray-400 mx-auto mb-4" />
        <h3 className="text-lg font-semibold text-gray-700">Aucune absence enregistrée</h3>
        <p className="text-sm text-gray-500 mt-1">L'historique des absences de cet employé est vide.</p>
      </div>
    );
  }
  
  const handleRowClick = (absenceId: string) => {
    setExpandedRowId(prevId => (prevId === absenceId ? null : absenceId));
  };


  return (
    <div className="overflow-x-auto">
      <table className="min-w-full divide-y divide-gray-200">
        <thead className="bg-gray-50">
          <tr>
            <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Période</th>
            <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Motif</th>
            <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Statut</th>
            <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Justificatif</th>
            <th scope="col" className="relative px-6 py-3"><span className="sr-only">Détails</span></th>
          </tr>
        </thead>
        <tbody className="bg-white divide-y divide-gray-200">
          {absences.map((absence) => (
            <React.Fragment key={absence.id}>
              <tr onClick={() => absence.attachment_url && handleRowClick(absence.id)} className={absence.attachment_url ? "cursor-pointer hover:bg-gray-50" : ""}>
                <td className="px-6 py-4 whitespace-nowrap">
                  <div className="text-sm text-gray-900 font-medium">{new Date(absence.start_date).toLocaleDateString('fr-FR')} - {new Date(absence.end_date).toLocaleDateString('fr-FR')}</div>
                </td>
                <td className="px-6 py-4">
                  <div className="text-sm text-gray-900">{absence.reason}</div>
                </td>
                <td className="px-6 py-4 whitespace-nowrap">
                  {absence.justified ? (
                    <span className="px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-green-100 text-green-800">Justifiée</span>
                  ) : (
                    <span className="px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-red-100 text-red-800">Non justifiée</span>
                  )}
                </td>
                <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                  {absence.attachment_url ? (
                    <a href={absence.attachment_url} target="_blank" rel="noopener noreferrer" onClick={e => e.stopPropagation()} className="text-[--org-color-main] hover:underline flex items-center font-semibold">
                      <Icon.Download className="h-4 w-4 mr-1" />
                      Voir
                    </a>
                  ) : (
                    'Aucun'
                  )}
                </td>
                <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                    {absence.attachment_url && (
                        <Icon.ChevronRight className={`h-5 w-5 text-gray-400 transition-transform duration-200 ${expandedRowId === absence.id ? 'transform rotate-90' : ''}`} />
                    )}
                </td>
              </tr>
              {expandedRowId === absence.id && (
                  <tr>
                      <td colSpan={5} className="p-0">
                         <AnalysisDetail absence={absence} employeeName={employeeName} />
                      </td>
                  </tr>
              )}
            </React.Fragment>
          ))}
        </tbody>
      </table>
    </div>
  );
};