import React from 'react';
import ReactDOM from 'react-dom/client';
import { App } from './App';
import { NotificationProvider } from './contexts/NotificationContext';
import * as Sentry from "@sentry/react";

// Initialize Sentry for error monitoring.
// The DSN should be replaced with your actual Sentry project DSN.
Sentry.init({
  dsn: "https://<EMAIL>/0",
  integrations: [
    Sentry.browserTracingIntegration(),
    Sentry.replayIntegration(),
  ],
  // Performance Monitoring
  tracesSampleRate: 1.0, 
  // Session Replay
  replaysSessionSampleRate: 0.1, 
  replaysOnErrorSampleRate: 1.0, 
});

const rootElement = document.getElementById('root');
if (!rootElement) {
  throw new Error("Could not find root element to mount to");
}

const root = ReactDOM.createRoot(rootElement);
root.render(
  <React.StrictMode>
    <Sentry.ErrorBoundary fallback={<div className="flex items-center justify-center h-screen bg-[--lica-blue-light]"><p className="text-lg text-[--lica-blue-dark] font-semibold">Une erreur est survenue. L'équipe technique a été notifiée.</p></div>}>
      <NotificationProvider>
        <App />
      </NotificationProvider>
    </Sentry.ErrorBoundary>
  </React.StrictMode>
);
