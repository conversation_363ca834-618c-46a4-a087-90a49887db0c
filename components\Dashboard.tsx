import React from 'react';
import { Employee, ReportSummary, Anomaly } from '../types';
import { Card } from './shared/Card';
import { Icon } from './shared/Icon';

interface DashboardProps {
  employees: Employee[];
  absenceSummary: ReportSummary | null;
  absenceAnomalies: Anomaly[];
  onNavigateToReports: () => void;
}

export const Dashboard: React.FC<DashboardProps> = ({ employees, absenceSummary, absenceAnomalies, onNavigateToReports }) => {
    const activeEmployeesCount = employees.length;

    // Affichage message quota Gemini dépassé si une anomalie spéciale est présente
    let quotaErrorMessage = null;
    if (absenceAnomalies && Array.isArray(absenceAnomalies)) {
        const quotaError = absenceAnomalies.find(a => a.description?.includes('Quota Gemini API dépassé'));
        if (quotaError) {
            quotaErrorMessage = (
                <div className="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded mb-4">
                    <strong>Attention :</strong> {quotaError.description}
                </div>
            );
        }
    }

    return (
        <div>
            {quotaErrorMessage}
      <div className="flex justify-between items-center mb-8 pb-4 border-b border-gray-200">
        <h1 className="text-3xl font-bold text-[--org-color-main] uppercase tracking-wide">Tableau de bord</h1>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-3 gap-6 mb-8">
        <Card className="md:col-span-2 bg-gradient-to-r from-[--org-color-main] to-[--org-color-dark] text-white p-8">
            <h2 className="text-2xl font-bold">Bienvenue sur votre espace d'audit.</h2>
            <p className="mt-2 opacity-90">Utilisez les onglets de navigation pour gérer les employés, les documents et analyser la paie.</p>
        </Card>
         <Card>
            <div className="flex items-center">
                <div className="p-3 bg-blue-100 rounded-full mr-4"><Icon.Users className="h-6 w-6 text-blue-600"/></div>
                <div>
                    <p className="text-sm font-medium text-gray-500">Employés Actifs</p>
                    <p className="text-3xl font-bold text-gray-800">{activeEmployeesCount}</p>
                </div>
            </div>
        </Card>
      </div>

       <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
        <Card>
            <button onClick={onNavigateToReports} className="w-full text-left">
                <h3 className="font-semibold text-gray-800 mb-4">Synthèse des Absences</h3>
                <div className="flex justify-around items-center text-center">
                    <div>
                        <p className="text-4xl font-bold text-[--org-color-main]">{absenceSummary?.total ?? 0}</p>
                        <p className="text-sm text-gray-500">Absences Totales</p>
                    </div>
                    <div>
                        <p className="text-4xl font-bold text-red-500">{absenceSummary?.unjustified ?? 0}</p>
                        <p className="text-sm text-gray-500">Non Justifiées</p>
                    </div>
                    <div>
                        <p className="text-4xl font-bold text-yellow-500">{absenceSummary?.anomaliesCount ?? 0}</p>
                        <p className="text-sm text-gray-500">Anomalies IA</p>
                    </div>
                </div>
                 <div className="text-right mt-4 text-sm font-semibold text-[--org-color-main] hover:underline">
                    Voir le rapport détaillé →
                </div>
            </button>
        </Card>
        <Card>
            <div className="text-center py-10 h-full flex flex-col justify-center items-center">
                <Icon.LicaFullLogo className="h-16 w-auto mx-auto text-gray-300" />
                <p className="font-semibold text-gray-600 mt-4">Prêt à commencer l'audit</p>
                <p className="text-sm text-gray-500">Sélectionnez une fonctionnalité dans le menu de gauche.</p>
            </div>
       </Card>
       </div>
    </div>
  );
};